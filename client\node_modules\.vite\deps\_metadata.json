{"hash": "862d0da4", "configHash": "7fef20cd", "lockfileHash": "f3771a72", "browserHash": "f23a6d56", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e3a56145", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "19632624", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "fb72cee1", "needsInterop": true}, "@clerk/clerk-react": {"src": "../../@clerk/clerk-react/dist/index.mjs", "file": "@clerk_clerk-react.js", "fileHash": "1532336a", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "2a9984fd", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "5e950bf8", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "403f7832", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "822ebd5a", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "0cead9d3", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "83c1167c", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "c375e940", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "90d45786", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "7bfbc3a6", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "a26966f9", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "c39e28b9", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "c7f7a465", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "4d853299", "needsInterop": false}, "cmdk": {"src": "../../cmdk/dist/index.mjs", "file": "cmdk.js", "fileHash": "4dd8017c", "needsInterop": false}, "convex/react": {"src": "../../convex/dist/esm/react/index.js", "file": "convex_react.js", "fileHash": "b78c3116", "needsInterop": false}, "convex/react-clerk": {"src": "../../convex/dist/esm/react-clerk/index.js", "file": "convex_react-clerk.js", "fileHash": "cf38d59c", "needsInterop": false}, "convex/server": {"src": "../../convex/dist/esm/server/index.js", "file": "convex_server.js", "fileHash": "13f8e03f", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "42febcb8", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "3a03a198", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "bc35ae56", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "410aca3e", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "427374da", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "c41cc67d", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "fbb2656d", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "0795decc", "needsInterop": false}}, "chunks": {"chunk-TQI5D6KO": {"file": "chunk-TQI5D6KO.js"}, "chunk-R35BAXRD": {"file": "chunk-R35BAXRD.js"}, "chunk-QH7Y6EI7": {"file": "chunk-QH7Y6EI7.js"}, "chunk-D4FGT6Q6": {"file": "chunk-D4FGT6Q6.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-SPYKA5V4": {"file": "chunk-SPYKA5V4.js"}, "chunk-JB6ZCZQC": {"file": "chunk-JB6ZCZQC.js"}, "chunk-HGJTK426": {"file": "chunk-HGJTK426.js"}, "chunk-TH62OU6K": {"file": "chunk-TH62OU6K.js"}, "chunk-PFCZ3SAG": {"file": "chunk-PFCZ3SAG.js"}, "chunk-RJLARJ2F": {"file": "chunk-RJLARJ2F.js"}, "chunk-OXQJEXZG": {"file": "chunk-OXQJEXZG.js"}, "chunk-NFCKRDSA": {"file": "chunk-NFCKRDSA.js"}, "chunk-VHZW6DWW": {"file": "chunk-VHZW6DWW.js"}, "chunk-2MHJNVUX": {"file": "chunk-2MHJNVUX.js"}, "chunk-2V3IRYIZ": {"file": "chunk-2V3IRYIZ.js"}, "chunk-VMKDFUY6": {"file": "chunk-VMKDFUY6.js"}, "chunk-IIEH4KGC": {"file": "chunk-IIEH4KGC.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-JFWPD3MN": {"file": "chunk-JFWPD3MN.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}