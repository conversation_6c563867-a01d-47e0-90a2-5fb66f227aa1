{"version": 3, "sources": ["../../convex/src/react/use_subscription.ts", "../../convex/src/react/use_queries.ts", "../../convex/src/browser/logging.ts", "../../convex/src/browser/sync/udf_path_utils.ts", "../../convex/src/browser/sync/local_state.ts", "../../convex/src/browser/sync/request_manager.ts", "../../convex/src/browser/sync/optimistic_updates_impl.ts", "../../convex/src/browser/long.ts", "../../convex/src/browser/sync/remote_query_set.ts", "../../convex/src/browser/sync/protocol.ts", "../../convex/src/browser/sync/web_socket_manager.ts", "../../convex/src/browser/sync/session.ts", "../../jwt-decode/build/esm/index.js", "../../convex/src/browser/sync/authentication_manager.ts", "../../convex/src/browser/sync/metrics.ts", "../../convex/src/browser/sync/client.ts", "../../convex/src/react/client.ts", "../../convex/src/react/queries_observer.ts", "../../convex/src/react/ConvexAuthState.tsx"], "sourcesContent": ["import { useEffect, useState } from \"react\";\n\n/*\nThis code is taken from https://gist.github.com/bvaughn/e25397f70e8c65b0ae0d7c90b731b189\nbecause correct subscriptions in async React is complex!\n\nIt could probably be replaced with `useSyncExternalStore()`.\n\nThe MIT License (MIT)\nCopyright © 2023 Brian Vaughn\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the “Software”), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\n/**\n * Hook used for safely managing subscriptions in concurrent mode.\n *\n * In order to avoid removing and re-adding subscriptions each time this hook is called,\n * the parameters passed to this hook should be memoized in some way–\n * either by wrapping the entire params object with useMemo()\n * or by wrapping the individual callbacks with useCallback().\n *\n * @internal\n */\nexport function useSubscription<Value>({\n  // (Synchronously) returns the current value of our subscription.\n  getCurrentValue,\n\n  // This function is passed an event handler to attach to the subscription.\n  // It should return an unsubscribe function that removes the handler.\n  subscribe,\n}: {\n  getCurrentValue: () => Value;\n  subscribe: (callback: () => void) => () => void;\n}): Value {\n  // Read the current value from our subscription.\n  // When this value changes, we'll schedule an update with React.\n  // It's important to also store the hook params so that we can check for staleness.\n  // (See the comment in checkForUpdates() below for more info.)\n  const [state, setState] = useState(() => ({\n    getCurrentValue,\n    subscribe,\n    value: getCurrentValue(),\n  }));\n\n  let valueToReturn = state.value;\n\n  // If parameters have changed since our last render, schedule an update with its current value.\n  if (\n    state.getCurrentValue !== getCurrentValue ||\n    state.subscribe !== subscribe\n  ) {\n    // If the subscription has been updated, we'll schedule another update with React.\n    // React will process this update immediately, so the old subscription value won't be committed.\n    // It is still nice to avoid returning a mismatched value though, so let's override the return value.\n    valueToReturn = getCurrentValue();\n\n    setState({\n      getCurrentValue,\n      subscribe,\n      value: valueToReturn,\n    });\n  }\n\n  // It is important not to subscribe while rendering because this can lead to memory leaks.\n  // (Learn more at reactjs.org/docs/strict-mode.html#detecting-unexpected-side-effects)\n  // Instead, we wait until the commit phase to attach our handler.\n  //\n  // We intentionally use a passive effect (useEffect) rather than a synchronous one (useLayoutEffect)\n  // so that we don't stretch the commit phase.\n  // This also has an added benefit when multiple components are subscribed to the same source:\n  // It allows each of the event handlers to safely schedule work without potentially removing an another handler.\n  // (Learn more at https://codesandbox.io/s/k0yvr5970o)\n  useEffect(() => {\n    let didUnsubscribe = false;\n\n    const checkForUpdates = () => {\n      // It's possible that this callback will be invoked even after being unsubscribed,\n      // if it's removed as a result of a subscription event/update.\n      // In this case, React will log a DEV warning about an update from an unmounted component.\n      // We can avoid triggering that warning with this check.\n      if (didUnsubscribe) {\n        return;\n      }\n\n      setState((prevState) => {\n        // Ignore values from stale sources!\n        // Since we subscribe an unsubscribe in a passive effect,\n        // it's possible that this callback will be invoked for a stale (previous) subscription.\n        // This check avoids scheduling an update for that stale subscription.\n        if (\n          prevState.getCurrentValue !== getCurrentValue ||\n          prevState.subscribe !== subscribe\n        ) {\n          return prevState;\n        }\n\n        // Some subscriptions will auto-invoke the handler, even if the value hasn't changed.\n        // If the value hasn't changed, no update is needed.\n        // Return state as-is so React can bail out and avoid an unnecessary render.\n        const value = getCurrentValue();\n        if (prevState.value === value) {\n          return prevState;\n        }\n\n        return { ...prevState, value };\n      });\n    };\n    const unsubscribe = subscribe(checkForUpdates);\n\n    // Because we're subscribing in a passive effect,\n    // it's possible that an update has occurred between render and our effect handler.\n    // Check for this and schedule an update if work has occurred.\n    checkForUpdates();\n\n    return () => {\n      didUnsubscribe = true;\n      unsubscribe();\n    };\n  }, [getCurrentValue, subscribe]);\n\n  // Return the current value for our caller to use while rendering.\n  return valueToReturn;\n}\n", "import { Value } from \"../values/index.js\";\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useConvex } from \"./client.js\";\nimport { CreateWatch, QueriesObserver } from \"./queries_observer.js\";\nimport { useSubscription } from \"./use_subscription.js\";\nimport { QueryJournal } from \"../browser/index.js\";\nimport { FunctionReference } from \"../server/api.js\";\n\n/**\n * Load a variable number of reactive Convex queries.\n *\n * `useQueries` is similar to {@link useQuery} but it allows\n * loading multiple queries which can be useful for loading a dynamic number\n * of queries without violating the rules of React hooks.\n *\n * This hook accepts an object whose keys are identifiers for each query and the\n * values are objects of `{ query: FunctionReference, args: Record<string, Value> }`. The\n * `query` is a FunctionReference for the Convex query function to load, and the `args` are\n * the arguments to that function.\n *\n * The hook returns an object that maps each identifier to the result of the query,\n * `undefined` if the query is still loading, or an instance of `Error` if the query\n * threw an exception.\n *\n * For example if you loaded a query like:\n * ```typescript\n * const results = useQueries({\n *   messagesInGeneral: {\n *     query: \"listMessages\",\n *     args: { channel: \"#general\" }\n *   }\n * });\n * ```\n * then the result would look like:\n * ```typescript\n * {\n *   messagesInGeneral: [{\n *     channel: \"#general\",\n *     body: \"hello\"\n *     _id: ...,\n *     _creationTime: ...\n *   }]\n * }\n * ```\n *\n * This React hook contains internal state that will cause a rerender\n * whenever any of the query results change.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param queries - An object mapping identifiers to objects of\n * `{query: string, args: Record<string, Value> }` describing which query\n * functions to fetch.\n * @returns An object with the same keys as the input. The values are the result\n * of the query function, `undefined` if it's still loading, or an `Error` if\n * it threw an exception.\n *\n * @public\n */\nexport function useQueries(\n  queries: RequestForQueries,\n): Record<string, any | undefined | Error> {\n  const convex = useConvex();\n  if (convex === undefined) {\n    // Error message includes `useQuery` because this hook is called by `useQuery`\n    // more often than it's called directly.\n    throw new Error(\n      \"Could not find Convex client! `useQuery` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n  const createWatch = useMemo(() => {\n    return (\n      query: FunctionReference<\"query\">,\n      args: Record<string, Value>,\n      journal?: QueryJournal,\n    ) => {\n      return convex.watchQuery(query, args, { journal });\n    };\n  }, [convex]);\n  return useQueriesHelper(queries, createWatch);\n}\n\n/**\n * Internal version of `useQueries` that is exported for testing.\n */\nexport function useQueriesHelper(\n  queries: RequestForQueries,\n  createWatch: CreateWatch,\n): Record<string, any | undefined | Error> {\n  const [observer] = useState(() => new QueriesObserver(createWatch));\n\n  if (observer.createWatch !== createWatch) {\n    observer.setCreateWatch(createWatch);\n  }\n\n  // Unsubscribe from all queries on unmount.\n  useEffect(() => () => observer.destroy(), [observer]);\n\n  const subscription = useMemo(\n    () => ({\n      getCurrentValue: () => {\n        return observer.getLocalResults(queries);\n      },\n      subscribe: (callback: () => void) => {\n        observer.setQueries(queries);\n        return observer.subscribe(callback);\n      },\n    }),\n    [observer, queries],\n  );\n\n  return useSubscription(subscription);\n}\n\n/**\n * An object representing a request to load multiple queries.\n *\n * The keys of this object are identifiers and the values are objects containing\n * the query function and the arguments to pass to it.\n *\n * This is used as an argument to {@link useQueries}.\n * @public\n */\nexport type RequestForQueries = Record<\n  string,\n  {\n    query: FunctionReference<\"query\">;\n    args: Record<string, Value>;\n  }\n>;\n", "/* eslint-disable no-console */ // This is the one file where we can `console.log` for the default logger implementation.\nimport { ConvexError, Value } from \"../values/index.js\";\nimport { FunctionFailure } from \"./sync/function_result.js\";\n\n// This is blue #9 from https://www.radix-ui.com/docs/colors/palette-composition/the-scales\n// It must look good in both light and dark mode.\nconst INFO_COLOR = \"color:rgb(0, 145, 255)\";\n\nexport type UdfType = \"query\" | \"mutation\" | \"action\" | \"any\";\n\nfunction prefix_for_source(source: UdfType) {\n  switch (source) {\n    case \"query\":\n      return \"Q\";\n    case \"mutation\":\n      return \"M\";\n    case \"action\":\n      return \"A\";\n    case \"any\":\n      return \"?\";\n  }\n}\n\nexport type LogLevel = \"debug\" | \"info\" | \"warn\" | \"error\";\n\n/**\n * A logger that can be used to log messages. By default, this is a wrapper\n * around `console`, but can be configured to not log at all or to log somewhere\n * else.\n */\nexport type Logger = {\n  logVerbose(...args: any[]): void;\n  log(...args: any[]): void;\n  warn(...args: any[]): void;\n  error(...args: any[]): void;\n};\n\nexport class DefaultLogger implements Logger {\n  private _onLogLineFuncs: Record<\n    string,\n    (level: LogLevel, ...args: any[]) => void\n  >;\n  private _verbose: boolean;\n\n  constructor(options: { verbose: boolean }) {\n    this._onLogLineFuncs = {};\n    this._verbose = options.verbose;\n  }\n\n  addLogLineListener(\n    func: (level: LogLevel, ...args: any[]) => void,\n  ): () => void {\n    let id = Math.random().toString(36).substring(2, 15);\n    for (let i = 0; i < 10; i++) {\n      if (this._onLogLineFuncs[id] === undefined) {\n        break;\n      }\n      id = Math.random().toString(36).substring(2, 15);\n    }\n    this._onLogLineFuncs[id] = func;\n    return () => {\n      delete this._onLogLineFuncs[id];\n    };\n  }\n\n  logVerbose(...args: any[]) {\n    if (this._verbose) {\n      for (const func of Object.values(this._onLogLineFuncs)) {\n        func(\"debug\", `${new Date().toISOString()}`, ...args);\n      }\n    }\n  }\n\n  log(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"info\", ...args);\n    }\n  }\n\n  warn(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"warn\", ...args);\n    }\n  }\n\n  error(...args: any[]) {\n    for (const func of Object.values(this._onLogLineFuncs)) {\n      func(\"error\", ...args);\n    }\n  }\n}\n\nexport function instantiateDefaultLogger(options: {\n  verbose: boolean;\n}): Logger {\n  const logger = new DefaultLogger(options);\n  logger.addLogLineListener((level, ...args) => {\n    switch (level) {\n      case \"debug\":\n        console.debug(...args);\n        break;\n      case \"info\":\n        console.log(...args);\n        break;\n      case \"warn\":\n        console.warn(...args);\n        break;\n      case \"error\":\n        console.error(...args);\n        break;\n      default: {\n        level satisfies never;\n        console.log(...args);\n      }\n    }\n  });\n  return logger;\n}\n\nexport function instantiateNoopLogger(options: { verbose: boolean }): Logger {\n  return new DefaultLogger(options);\n}\n\nexport function logForFunction(\n  logger: Logger,\n  type: \"info\" | \"error\",\n  source: UdfType,\n  udfPath: string,\n  message: string | { errorData: Value },\n) {\n  const prefix = prefix_for_source(source);\n\n  if (typeof message === \"object\") {\n    message = `ConvexError ${JSON.stringify(message.errorData, null, 2)}`;\n  }\n  if (type === \"info\") {\n    const match = message.match(/^\\[.*?\\] /);\n    if (match === null) {\n      logger.error(\n        `[CONVEX ${prefix}(${udfPath})] Could not parse console.log`,\n      );\n      return;\n    }\n    const level = message.slice(1, match[0].length - 2);\n    const args = message.slice(match[0].length);\n\n    logger.log(`%c[CONVEX ${prefix}(${udfPath})] [${level}]`, INFO_COLOR, args);\n  } else {\n    logger.error(`[CONVEX ${prefix}(${udfPath})] ${message}`);\n  }\n}\n\nexport function logFatalError(logger: Logger, message: string): Error {\n  const errorMessage = `[CONVEX FATAL ERROR] ${message}`;\n  logger.error(errorMessage);\n  return new Error(errorMessage);\n}\n\nexport function createHybridErrorStacktrace(\n  source: UdfType,\n  udfPath: string,\n  result: FunctionFailure,\n): string {\n  const prefix = prefix_for_source(source);\n  return `[CONVEX ${prefix}(${udfPath})] ${result.errorMessage}\\n  Called by client`;\n}\n\nexport function forwardData(\n  result: FunctionFailure,\n  error: ConvexError<string>,\n) {\n  (error as ConvexError<any>).data = result.errorData;\n  return error;\n}\n", "import { convexTo<PERSON>son, Value } from \"../../values/index.js\";\n\nexport function canonicalizeUdfPath(udfPath: string): string {\n  const pieces = udfPath.split(\":\");\n  let moduleName: string;\n  let functionName: string;\n  if (pieces.length === 1) {\n    moduleName = pieces[0];\n    functionName = \"default\";\n  } else {\n    moduleName = pieces.slice(0, pieces.length - 1).join(\":\");\n    functionName = pieces[pieces.length - 1];\n  }\n  if (moduleName.endsWith(\".js\")) {\n    moduleName = moduleName.slice(0, -3);\n  }\n  return `${moduleName}:${functionName}`;\n}\n\n/**\n * A string representing the name and arguments of a query.\n *\n * This is used by the {@link BaseConvexClient}.\n *\n * @public\n */\nexport type QueryToken = string;\n\nexport function serializePathAndArgs(\n  udfPath: string,\n  args: Record<string, Value>,\n): QueryToken {\n  return JSON.stringify({\n    udfPath: canonicalizeUdfPath(udfPath),\n    args: convexT<PERSON><PERSON><PERSON>(args),\n  });\n}\n", "import { convexToJson, Value } from \"../../values/index.js\";\nimport {\n  AddQuery,\n  RemoveQuery,\n  QueryId,\n  QuerySetModification,\n  QuerySetVersion,\n  IdentityVersion,\n  Authenticate,\n  QueryJournal,\n  Transition,\n  AdminAuthentication,\n  UserIdentityAttributes,\n} from \"./protocol.js\";\nimport {\n  canonicalizeUdfPath,\n  QueryToken,\n  serializePathAndArgs,\n} from \"./udf_path_utils.js\";\n\ntype LocalQuery = {\n  id: QueryId;\n  canonicalizedUdfPath: string;\n  args: Record<string, Value>;\n  numSubscribers: number;\n  journal?: QueryJournal;\n  componentPath?: string;\n};\n\nexport class LocalSyncState {\n  private nextQueryId: QueryId;\n  private querySetVersion: QuerySetVersion;\n  private readonly querySet: Map<QueryToken, LocalQuery>;\n  private readonly queryIdToToken: Map<QueryId, QueryToken>;\n  private identityVersion: IdentityVersion;\n  private auth?: {\n    tokenType: \"Admin\" | \"User\";\n    value: string;\n    impersonating?: UserIdentityAttributes;\n  };\n  private readonly outstandingQueriesOlderThanRestart: Set<QueryId>;\n  private outstandingAuthOlderThanRestart: boolean;\n  private paused: boolean;\n  private pendingQuerySetModifications: Map<QueryId, AddQuery | RemoveQuery>;\n\n  constructor() {\n    this.nextQueryId = 0;\n    this.querySetVersion = 0;\n    this.identityVersion = 0;\n    this.querySet = new Map();\n    this.queryIdToToken = new Map();\n    this.outstandingQueriesOlderThanRestart = new Set();\n    this.outstandingAuthOlderThanRestart = false;\n    this.paused = false;\n    this.pendingQuerySetModifications = new Map();\n  }\n\n  hasSyncedPastLastReconnect(): boolean {\n    return (\n      this.outstandingQueriesOlderThanRestart.size === 0 &&\n      !this.outstandingAuthOlderThanRestart\n    );\n  }\n\n  markAuthCompletion() {\n    this.outstandingAuthOlderThanRestart = false;\n  }\n\n  subscribe(\n    udfPath: string,\n    args: Record<string, Value>,\n    journal?: QueryJournal,\n    componentPath?: string,\n  ): {\n    queryToken: QueryToken;\n    modification: QuerySetModification | null;\n    unsubscribe: () => QuerySetModification | null;\n  } {\n    const canonicalizedUdfPath = canonicalizeUdfPath(udfPath);\n    const queryToken = serializePathAndArgs(canonicalizedUdfPath, args);\n\n    const existingEntry = this.querySet.get(queryToken);\n\n    if (existingEntry !== undefined) {\n      existingEntry.numSubscribers += 1;\n      return {\n        queryToken,\n        modification: null,\n        unsubscribe: () => this.removeSubscriber(queryToken),\n      };\n    } else {\n      const queryId = this.nextQueryId++;\n      const query: LocalQuery = {\n        id: queryId,\n        canonicalizedUdfPath,\n        args,\n        numSubscribers: 1,\n        journal,\n        componentPath,\n      };\n      this.querySet.set(queryToken, query);\n      this.queryIdToToken.set(queryId, queryToken);\n\n      const baseVersion = this.querySetVersion;\n      const newVersion = this.querySetVersion + 1;\n\n      const add: AddQuery = {\n        type: \"Add\",\n        queryId,\n        udfPath: canonicalizedUdfPath,\n        args: [convexToJson(args)],\n        journal,\n        componentPath,\n      };\n\n      if (this.paused) {\n        this.pendingQuerySetModifications.set(queryId, add);\n      } else {\n        this.querySetVersion = newVersion;\n      }\n\n      const modification: QuerySetModification = {\n        type: \"ModifyQuerySet\",\n        baseVersion,\n        newVersion,\n        modifications: [add],\n      };\n      return {\n        queryToken,\n        modification,\n        unsubscribe: () => this.removeSubscriber(queryToken),\n      };\n    }\n  }\n\n  transition(transition: Transition) {\n    for (const modification of transition.modifications) {\n      switch (modification.type) {\n        case \"QueryUpdated\":\n        case \"QueryFailed\": {\n          this.outstandingQueriesOlderThanRestart.delete(modification.queryId);\n          const journal = modification.journal;\n          if (journal !== undefined) {\n            const queryToken = this.queryIdToToken.get(modification.queryId);\n            // We may have already unsubscribed to this query by the time the server\n            // sends us the journal. If so, just ignore it.\n            if (queryToken !== undefined) {\n              this.querySet.get(queryToken)!.journal = journal;\n            }\n          }\n\n          break;\n        }\n        case \"QueryRemoved\": {\n          this.outstandingQueriesOlderThanRestart.delete(modification.queryId);\n          break;\n        }\n        default: {\n          // Enforce that the switch-case is exhaustive.\n          modification satisfies never;\n          throw new Error(`Invalid modification ${(modification as any).type}`);\n        }\n      }\n    }\n  }\n\n  queryId(udfPath: string, args: Record<string, Value>): QueryId | null {\n    const canonicalizedUdfPath = canonicalizeUdfPath(udfPath);\n    const queryToken = serializePathAndArgs(canonicalizedUdfPath, args);\n    const existingEntry = this.querySet.get(queryToken);\n    if (existingEntry !== undefined) {\n      return existingEntry.id;\n    }\n    return null;\n  }\n\n  isCurrentOrNewerAuthVersion(version: IdentityVersion): boolean {\n    return version >= this.identityVersion;\n  }\n\n  setAuth(value: string): Authenticate {\n    this.auth = {\n      tokenType: \"User\",\n      value: value,\n    };\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      baseVersion: baseVersion,\n      ...this.auth,\n    };\n  }\n\n  setAdminAuth(\n    value: string,\n    actingAs?: UserIdentityAttributes,\n  ): AdminAuthentication {\n    const auth: typeof this.auth & {\n      tokenType: \"Admin\";\n    } = {\n      tokenType: \"Admin\",\n      value,\n      impersonating: actingAs,\n    };\n    this.auth = auth;\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      baseVersion: baseVersion,\n      ...auth,\n    };\n  }\n\n  clearAuth(): Authenticate {\n    this.auth = undefined;\n    this.markAuthCompletion();\n    const baseVersion = this.identityVersion;\n    if (!this.paused) {\n      this.identityVersion = baseVersion + 1;\n    }\n    return {\n      type: \"Authenticate\",\n      tokenType: \"None\",\n      baseVersion: baseVersion,\n    };\n  }\n\n  hasAuth(): boolean {\n    return !!this.auth;\n  }\n\n  isNewAuth(value: string): boolean {\n    return this.auth?.value !== value;\n  }\n\n  queryPath(queryId: QueryId): string | null {\n    const pathAndArgs = this.queryIdToToken.get(queryId);\n    if (pathAndArgs) {\n      return this.querySet.get(pathAndArgs)!.canonicalizedUdfPath;\n    }\n    return null;\n  }\n\n  queryArgs(queryId: QueryId): Record<string, Value> | null {\n    const pathAndArgs = this.queryIdToToken.get(queryId);\n    if (pathAndArgs) {\n      return this.querySet.get(pathAndArgs)!.args;\n    }\n    return null;\n  }\n\n  queryToken(queryId: QueryId): string | null {\n    return this.queryIdToToken.get(queryId) ?? null;\n  }\n\n  queryJournal(queryToken: QueryToken): QueryJournal | undefined {\n    return this.querySet.get(queryToken)?.journal;\n  }\n\n  restart(\n    oldRemoteQueryResults: Set<QueryId>,\n  ): [QuerySetModification, Authenticate?] {\n    // Restart works whether we are paused or unpaused.\n    // The `this.pendingQuerySetModifications` is not used\n    // when restarting as the AddQuery and RemoveQuery are computed\n    // from scratch, based on the old remote query results, here.\n    this.unpause();\n\n    this.outstandingQueriesOlderThanRestart.clear();\n    const modifications = [];\n    for (const localQuery of this.querySet.values()) {\n      const add: AddQuery = {\n        type: \"Add\",\n        queryId: localQuery.id,\n        udfPath: localQuery.canonicalizedUdfPath,\n        args: [convexToJson(localQuery.args)],\n        journal: localQuery.journal,\n        componentPath: localQuery.componentPath,\n      };\n      modifications.push(add);\n\n      if (!oldRemoteQueryResults.has(localQuery.id)) {\n        this.outstandingQueriesOlderThanRestart.add(localQuery.id);\n      }\n    }\n    this.querySetVersion = 1;\n    const querySet: QuerySetModification = {\n      type: \"ModifyQuerySet\",\n      baseVersion: 0,\n      newVersion: 1,\n      modifications,\n    };\n    // If there's no auth, no need to send an update as the server will also start with an unknown identity.\n    if (!this.auth) {\n      this.identityVersion = 0;\n      return [querySet, undefined];\n    }\n    this.outstandingAuthOlderThanRestart = true;\n    const authenticate: Authenticate = {\n      type: \"Authenticate\",\n      baseVersion: 0,\n      ...this.auth,\n    };\n    this.identityVersion = 1;\n    return [querySet, authenticate];\n  }\n\n  pause() {\n    this.paused = true;\n  }\n\n  resume(): [QuerySetModification?, Authenticate?] {\n    const querySet: QuerySetModification | undefined =\n      this.pendingQuerySetModifications.size > 0\n        ? {\n            type: \"ModifyQuerySet\",\n            baseVersion: this.querySetVersion,\n            newVersion: ++this.querySetVersion,\n            modifications: Array.from(\n              this.pendingQuerySetModifications.values(),\n            ),\n          }\n        : undefined;\n    const authenticate: Authenticate | undefined =\n      this.auth !== undefined\n        ? {\n            type: \"Authenticate\",\n            baseVersion: this.identityVersion++,\n            ...this.auth,\n          }\n        : undefined;\n\n    this.unpause();\n\n    return [querySet, authenticate];\n  }\n\n  private unpause() {\n    this.paused = false;\n    this.pendingQuerySetModifications.clear();\n  }\n\n  private removeSubscriber(\n    queryToken: QueryToken,\n  ): QuerySetModification | null {\n    const localQuery = this.querySet.get(queryToken)!;\n\n    if (localQuery.numSubscribers > 1) {\n      localQuery.numSubscribers -= 1;\n      return null;\n    } else {\n      this.querySet.delete(queryToken);\n      this.queryIdToToken.delete(localQuery.id);\n      this.outstandingQueriesOlderThanRestart.delete(localQuery.id);\n      const baseVersion = this.querySetVersion;\n      const newVersion = this.querySetVersion + 1;\n      const remove: RemoveQuery = {\n        type: \"Remove\",\n        queryId: localQuery.id,\n      };\n      if (this.paused) {\n        if (this.pendingQuerySetModifications.has(localQuery.id)) {\n          this.pendingQuerySetModifications.delete(localQuery.id);\n        } else {\n          this.pendingQuerySetModifications.set(localQuery.id, remove);\n        }\n      } else {\n        this.querySetVersion = newVersion;\n      }\n      return {\n        type: \"ModifyQuerySet\",\n        baseVersion,\n        newVersion,\n        modifications: [remove],\n      };\n    }\n  }\n}\n", "import { jsonToConvex } from \"../../values/index.js\";\nimport { logForFunction, Logger } from \"../logging.js\";\nimport { Long } from \"../long.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport {\n  ActionRequest,\n  ActionResponse,\n  ClientMessage,\n  MutationRequest,\n  MutationResponse,\n  RequestId,\n} from \"./protocol.js\";\n\ntype RequestStatus =\n  | {\n      status: \"Requested\" | \"NotSent\";\n      onResult: (result: FunctionResult) => void;\n      requestedAt: Date;\n    }\n  | {\n      status: \"Completed\";\n      result: FunctionResult;\n      onResolve: () => void;\n      ts: Long;\n    };\n\nexport class RequestManager {\n  private inflightRequests: Map<\n    RequestId,\n    {\n      message: MutationRequest | ActionRequest;\n      status: RequestStatus;\n    }\n  >;\n  private requestsOlderThanRestart: Set<RequestId>;\n  private inflightMutationsCount: number = 0;\n  private inflightActionsCount: number = 0;\n  constructor(\n    private readonly logger: Logger,\n    private readonly markConnectionStateDirty: () => void,\n  ) {\n    this.inflightRequests = new Map();\n    this.requestsOlderThanRestart = new Set();\n  }\n\n  request(\n    message: MutationRequest | ActionRequest,\n    sent: boolean,\n  ): Promise<FunctionResult> {\n    const result = new Promise<FunctionResult>((resolve) => {\n      const status = sent ? \"Requested\" : \"NotSent\";\n      this.inflightRequests.set(message.requestId, {\n        message,\n        status: { status, requestedAt: new Date(), onResult: resolve },\n      });\n\n      if (message.type === \"Mutation\") {\n        this.inflightMutationsCount++;\n      } else if (message.type === \"Action\") {\n        this.inflightActionsCount++;\n      }\n    });\n\n    this.markConnectionStateDirty();\n    return result;\n  }\n\n  /**\n   * Update the state after receiving a response.\n   *\n   * @returns A RequestId if the request is complete and its optimistic update\n   * can be dropped, null otherwise.\n   */\n  onResponse(\n    response: MutationResponse | ActionResponse,\n  ): { requestId: RequestId; result: FunctionResult } | null {\n    const requestInfo = this.inflightRequests.get(response.requestId);\n    if (requestInfo === undefined) {\n      // Annoyingly we can occasionally get responses to mutations that we're no\n      // longer tracking. One flow where this happens is:\n      // 1. Client sends mutation 1\n      // 2. Client gets response for mutation 1. The sever says that it was committed at ts=10.\n      // 3. Client is disconnected\n      // 4. Client reconnects and re-issues queries and this mutation.\n      // 5. Server sends transition message to ts=20\n      // 6. Client drops mutation because it's already been observed.\n      // 7. Client receives a second response for mutation 1 but doesn't know about it anymore.\n\n      // The right fix for this is probably to add a reconciliation phase on\n      // reconnection where we receive responses to all the mutations before\n      // the transition message so this flow could never happen (CX-1513).\n\n      // For now though, we can just ignore this message.\n      return null;\n    }\n\n    // Because `.restart()` re-requests completed requests, we may get some\n    // responses for requests that are already in the \"Completed\" state.\n    // We can safely ignore those because we've already notified the UI about\n    // their results.\n    if (requestInfo.status.status === \"Completed\") {\n      return null;\n    }\n\n    const udfType =\n      requestInfo.message.type === \"Mutation\" ? \"mutation\" : \"action\";\n    const udfPath = requestInfo.message.udfPath;\n\n    for (const line of response.logLines) {\n      logForFunction(this.logger, \"info\", udfType, udfPath, line);\n    }\n\n    const status = requestInfo.status;\n    let result: FunctionResult;\n    let onResolve;\n    if (response.success) {\n      result = {\n        success: true,\n        logLines: response.logLines,\n        value: jsonToConvex(response.result),\n      };\n      onResolve = () => status.onResult(result);\n    } else {\n      const errorMessage = response.result as string;\n      const { errorData } = response;\n      logForFunction(this.logger, \"error\", udfType, udfPath, errorMessage);\n      result = {\n        success: false,\n        errorMessage,\n        errorData:\n          errorData !== undefined ? jsonToConvex(errorData) : undefined,\n        logLines: response.logLines,\n      };\n      onResolve = () => status.onResult(result);\n    }\n\n    // We can resolve Mutation failures immediately since they don't have any\n    // side effects. Actions are intentionally decoupled from\n    // queries/mutations here on the sync protocol since they have different\n    // guarantees.\n    if (response.type === \"ActionResponse\" || !response.success) {\n      onResolve();\n      this.inflightRequests.delete(response.requestId);\n      this.requestsOlderThanRestart.delete(response.requestId);\n\n      if (requestInfo.message.type === \"Action\") {\n        this.inflightActionsCount--;\n      } else if (requestInfo.message.type === \"Mutation\") {\n        this.inflightMutationsCount--;\n      }\n\n      this.markConnectionStateDirty();\n      return { requestId: response.requestId, result };\n    }\n\n    // We have to wait to resolve the request promise until after we transition\n    // past this timestamp so clients can read their own writes.\n    requestInfo.status = {\n      status: \"Completed\",\n      result,\n      ts: response.ts,\n      onResolve,\n    };\n\n    return null;\n  }\n\n  // Remove and returns completed requests.\n  removeCompleted(ts: Long): Map<RequestId, FunctionResult> {\n    const completeRequests: Map<RequestId, FunctionResult> = new Map();\n    for (const [requestId, requestInfo] of this.inflightRequests.entries()) {\n      const status = requestInfo.status;\n      if (status.status === \"Completed\" && status.ts.lessThanOrEqual(ts)) {\n        status.onResolve();\n        completeRequests.set(requestId, status.result);\n\n        if (requestInfo.message.type === \"Mutation\") {\n          this.inflightMutationsCount--;\n        } else if (requestInfo.message.type === \"Action\") {\n          this.inflightActionsCount--;\n        }\n\n        this.inflightRequests.delete(requestId);\n        this.requestsOlderThanRestart.delete(requestId);\n      }\n    }\n    if (completeRequests.size > 0) {\n      this.markConnectionStateDirty();\n    }\n    return completeRequests;\n  }\n\n  restart(): ClientMessage[] {\n    // When we reconnect to the backend, re-request all requests that are safe\n    // to be resend.\n\n    this.requestsOlderThanRestart = new Set(this.inflightRequests.keys());\n    const allMessages = [];\n    for (const [requestId, value] of this.inflightRequests) {\n      if (value.status.status === \"NotSent\") {\n        value.status.status = \"Requested\";\n        allMessages.push(value.message);\n        continue;\n      }\n\n      if (value.message.type === \"Mutation\") {\n        // This includes ones that have already been completed because we still\n        // want to tell the backend to transition the client past the completed\n        // timestamp. This is safe since mutations are idempotent.\n        allMessages.push(value.message);\n      } else if (value.message.type === \"Action\") {\n        // Unlike mutations, actions are not idempotent. When we reconnect to the\n        // backend, we don't know if it is safe to resend in-flight actions, so we\n        // cancel them and consider them failed.\n        this.inflightRequests.delete(requestId);\n        this.requestsOlderThanRestart.delete(requestId);\n        this.inflightActionsCount--;\n        if (value.status.status === \"Completed\") {\n          throw new Error(\"Action should never be in 'Completed' state\");\n        }\n        value.status.onResult({\n          success: false,\n          errorMessage: \"Connection lost while action was in flight\",\n          logLines: [],\n        });\n      }\n    }\n    this.markConnectionStateDirty();\n    return allMessages;\n  }\n\n  resume(): ClientMessage[] {\n    const allMessages = [];\n    for (const [, value] of this.inflightRequests) {\n      if (value.status.status === \"NotSent\") {\n        value.status.status = \"Requested\";\n        allMessages.push(value.message);\n        continue;\n      }\n    }\n    return allMessages;\n  }\n\n  /**\n   * @returns true if there are any requests that have been requested but have\n   * not be completed yet.\n   */\n  hasIncompleteRequests(): boolean {\n    for (const requestInfo of this.inflightRequests.values()) {\n      if (requestInfo.status.status === \"Requested\") {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * @returns true if there are any inflight requests, including ones that have\n   * completed on the server, but have not been applied.\n   */\n  hasInflightRequests(): boolean {\n    return this.inflightRequests.size > 0;\n  }\n\n  /**\n   * @returns true if there are any inflight requests, that have been hanging around\n   * since prior to the most recent restart.\n   */\n  hasSyncedPastLastReconnect(): boolean {\n    return this.requestsOlderThanRestart.size === 0;\n  }\n\n  timeOfOldestInflightRequest(): Date | null {\n    if (this.inflightRequests.size === 0) {\n      return null;\n    }\n    let oldestInflightRequest = Date.now();\n    for (const request of this.inflightRequests.values()) {\n      if (request.status.status !== \"Completed\") {\n        if (request.status.requestedAt.getTime() < oldestInflightRequest) {\n          oldestInflightRequest = request.status.requestedAt.getTime();\n        }\n      }\n    }\n    return new Date(oldestInflightRequest);\n  }\n\n  /**\n   * @returns The number of mutations currently in flight.\n   */\n  inflightMutations(): number {\n    return this.inflightMutationsCount;\n  }\n\n  /**\n   * @returns The number of actions currently in flight.\n   */\n  inflightActions(): number {\n    return this.inflightActionsCount;\n  }\n}\n", "import {\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  getFunctionName,\n} from \"../../server/api.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { Value } from \"../../values/index.js\";\nimport { createHybridErrorStacktrace, forwardData } from \"../logging.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport { OptimisticLocalStore } from \"./optimistic_updates.js\";\nimport { RequestId } from \"./protocol.js\";\nimport {\n  canonicalizeUdfPath,\n  QueryToken,\n  serializePathAndArgs,\n} from \"./udf_path_utils.js\";\nimport { ConvexError } from \"../../values/errors.js\";\n\n/**\n * An optimistic update function that has been curried over its arguments.\n */\ntype WrappedOptimisticUpdate = (locaQueryStore: OptimisticLocalStore) => void;\n\n/**\n * The implementation of `OptimisticLocalStore`.\n *\n * This class provides the interface for optimistic updates to modify query results.\n */\nclass OptimisticLocalStoreImpl implements OptimisticLocalStore {\n  // A references of the query results in OptimisticQueryResults\n  private readonly queryResults: QueryResultsMap;\n\n  // All of the queries modified by this class\n  readonly modifiedQueries: QueryToken[];\n\n  constructor(queryResults: QueryResultsMap) {\n    this.queryResults = queryResults;\n    this.modifiedQueries = [];\n  }\n\n  getQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): undefined | FunctionReturnType<Query> {\n    const queryArgs = parseArgs(args[0]);\n    const name = getFunctionName(query);\n    const queryResult = this.queryResults.get(\n      serializePathAndArgs(name, queryArgs),\n    );\n    if (queryResult === undefined) {\n      return undefined;\n    }\n    return OptimisticLocalStoreImpl.queryValue(queryResult.result);\n  }\n\n  getAllQueries<Query extends FunctionReference<\"query\">>(\n    query: Query,\n  ): {\n    args: FunctionArgs<Query>;\n    value: undefined | FunctionReturnType<Query>;\n  }[] {\n    const queriesWithName: {\n      args: FunctionArgs<Query>;\n      value: undefined | FunctionReturnType<Query>;\n    }[] = [];\n    const name = getFunctionName(query);\n    for (const queryResult of this.queryResults.values()) {\n      if (queryResult.udfPath === canonicalizeUdfPath(name)) {\n        queriesWithName.push({\n          args: queryResult.args as FunctionArgs<Query>,\n          value: OptimisticLocalStoreImpl.queryValue(queryResult.result),\n        });\n      }\n    }\n    return queriesWithName;\n  }\n\n  setQuery<QueryReference extends FunctionReference<\"query\">>(\n    queryReference: QueryReference,\n    args: FunctionArgs<QueryReference>,\n    value: undefined | FunctionReturnType<QueryReference>,\n  ): void {\n    const queryArgs = parseArgs(args);\n    const name = getFunctionName(queryReference);\n    const queryToken = serializePathAndArgs(name, queryArgs);\n\n    let result: FunctionResult | undefined;\n    if (value === undefined) {\n      result = undefined;\n    } else {\n      result = {\n        success: true,\n        value,\n        // It's an optimistic update, so there are no function logs to show.\n        logLines: [],\n      };\n    }\n    const query: Query = {\n      udfPath: name,\n      args: queryArgs,\n      result,\n    };\n    this.queryResults.set(queryToken, query);\n    this.modifiedQueries.push(queryToken);\n  }\n\n  private static queryValue(\n    result: FunctionResult | undefined,\n  ): Value | undefined {\n    if (result === undefined) {\n      return undefined;\n    } else if (result.success) {\n      return result.value;\n    } else {\n      // If the query is an error state, just return `undefined` as though\n      // it's loading. Optimistic updates should already handle `undefined` well\n      // and there isn't a need to break the whole update because it tried\n      // to load a single query that errored.\n      return undefined;\n    }\n  }\n}\n\ntype OptimisticUpdateAndId = {\n  update: WrappedOptimisticUpdate;\n  mutationId: RequestId;\n};\n\ntype Query = {\n  // undefined means the query was set to be loading (undefined) in an optimistic update.\n  // Note that we can also have queries not present in the QueryResultMap\n  // at all because they are still loading from the server and have no optimistic update\n  // setting an optimistic value in advance.\n  result: FunctionResult | undefined;\n  udfPath: string;\n  args: Record<string, Value>;\n};\nexport type QueryResultsMap = Map<QueryToken, Query>;\n\ntype ChangedQueries = QueryToken[];\n\n/**\n * A view of all of our query results with optimistic updates applied on top.\n */\nexport class OptimisticQueryResults {\n  private queryResults: QueryResultsMap;\n  private optimisticUpdates: OptimisticUpdateAndId[];\n\n  constructor() {\n    this.queryResults = new Map();\n    this.optimisticUpdates = [];\n  }\n\n  /**\n   * Apply all optimistic updates on top of server query results\n   */\n  ingestQueryResultsFromServer(\n    serverQueryResults: QueryResultsMap,\n    optimisticUpdatesToDrop: Set<RequestId>,\n  ): ChangedQueries {\n    this.optimisticUpdates = this.optimisticUpdates.filter((updateAndId) => {\n      return !optimisticUpdatesToDrop.has(updateAndId.mutationId);\n    });\n\n    const oldQueryResults = this.queryResults;\n    this.queryResults = new Map(serverQueryResults);\n    const localStore = new OptimisticLocalStoreImpl(this.queryResults);\n    for (const updateAndId of this.optimisticUpdates) {\n      updateAndId.update(localStore);\n    }\n\n    // To find the changed queries, just do a shallow comparison\n    // TODO(CX-733): Change this so we avoid unnecessary rerenders\n    const changedQueries: ChangedQueries = [];\n    for (const [queryToken, query] of this.queryResults) {\n      const oldQuery = oldQueryResults.get(queryToken);\n      if (oldQuery === undefined || oldQuery.result !== query.result) {\n        changedQueries.push(queryToken);\n      }\n    }\n\n    return changedQueries;\n  }\n\n  applyOptimisticUpdate(\n    update: WrappedOptimisticUpdate,\n    mutationId: RequestId,\n  ): ChangedQueries {\n    // Apply the update to our store\n    this.optimisticUpdates.push({\n      update,\n      mutationId,\n    });\n    const localStore = new OptimisticLocalStoreImpl(this.queryResults);\n    update(localStore);\n\n    // Notify about any query results that changed\n    // TODO(CX-733): Change this so we avoid unnecessary rerenders\n    return localStore.modifiedQueries;\n  }\n\n  /**\n   * @internal\n   */\n  rawQueryResult(queryToken: QueryToken): Query | undefined {\n    return this.queryResults.get(queryToken);\n  }\n\n  queryResult(queryToken: QueryToken): Value | undefined {\n    const query = this.queryResults.get(queryToken);\n    if (query === undefined) {\n      return undefined;\n    }\n    const result = query.result;\n    if (result === undefined) {\n      return undefined;\n    } else if (result.success) {\n      return result.value;\n    } else {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(\n            createHybridErrorStacktrace(\"query\", query.udfPath, result),\n          ),\n        );\n      }\n      throw new Error(\n        createHybridErrorStacktrace(\"query\", query.udfPath, result),\n      );\n    }\n  }\n\n  hasQueryResult(queryToken: QueryToken): boolean {\n    return this.queryResults.get(queryToken) !== undefined;\n  }\n\n  /**\n   * @internal\n   */\n  queryLogs(queryToken: QueryToken): string[] | undefined {\n    const query = this.queryResults.get(queryToken);\n    return query?.result?.logLines;\n  }\n}\n", "// Implements an unsigned long.\n// This is a subset of https://github.com/dcodeIO/Long.js,\n// vendored to decrease bundle size.\n// Copyright <PERSON> <<EMAIL>>\n// License: Apache Version 2.0\n/*\n\n                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"[]\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright 2023 Daniel Wirtz <<EMAIL>>\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n*/\n\n// This works... but don't try to compare one to a real Long.js Long!\n// For internal use only.\n// `| 0` assures the runtime that we are using integer arithmetic\nexport class Long {\n  low: number;\n  high: number;\n  __isUnsignedLong__: boolean;\n\n  static isLong(obj: Long) {\n    return (obj && obj.__isUnsignedLong__) === true;\n  }\n\n  constructor(low: number, high: number) {\n    this.low = low | 0;\n    this.high = high | 0;\n    this.__isUnsignedLong__ = true;\n  }\n\n  // prettier-ignore\n  static fromBytesLE(bytes: number[]): Long {\n    return new Long(\n      bytes[0] |\n      bytes[1] << 8 |\n      bytes[2] << 16 |\n      bytes[3] << 24,\n      bytes[4] |\n      bytes[5] << 8 |\n      bytes[6] << 16 |\n      bytes[7] << 24,\n    );\n  }\n\n  // prettier-ignore\n  toBytesLE() {\n    const hi = this.high;\n    const lo = this.low;\n    return [\n      lo & 0xff,\n      lo >>> 8 & 0xff,\n      lo >>> 16 & 0xff,\n      lo >>> 24,\n      hi & 0xff,\n      hi >>> 8 & 0xff,\n      hi >>> 16 & 0xff,\n      hi >>> 24\n    ];\n  }\n\n  static fromNumber(value: number) {\n    if (isNaN(value)) return UZERO;\n    if (value < 0) return UZERO;\n    if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n    return new Long(value % TWO_PWR_32_DBL | 0, (value / TWO_PWR_32_DBL) | 0);\n  }\n\n  toString() {\n    return (\n      BigInt(this.high) * BigInt(TWO_PWR_32_DBL) +\n      BigInt(this.low)\n    ).toString();\n  }\n\n  equals(other: Long) {\n    if (!Long.isLong(other)) other = Long.fromValue(other);\n    if (this.high >>> 31 === 1 && other.high >>> 31 === 1) return false;\n    return this.high === other.high && this.low === other.low;\n  }\n\n  notEquals(other: Long) {\n    return !this.equals(other);\n  }\n\n  comp(other: Long) {\n    if (!Long.isLong(other)) other = Long.fromValue(other);\n    if (this.equals(other)) return 0;\n    return other.high >>> 0 > this.high >>> 0 ||\n      (other.high === this.high && other.low >>> 0 > this.low >>> 0)\n      ? -1\n      : 1;\n  }\n\n  lessThanOrEqual(other: Long) {\n    return this.comp(/* validates */ other) <= 0;\n  }\n\n  static fromValue(val: any) {\n    if (typeof val === \"number\") return Long.fromNumber(val);\n    // Throws for non-objects, converts non-instanceof Long:\n    return new Long(val.low, val.high);\n  }\n}\n\nconst UZERO = new Long(0, 0);\nconst TWO_PWR_16_DBL = 1 << 16;\nconst TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\nconst TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\nconst MAX_UNSIGNED_VALUE = new Long(0xffffffff | 0, 0xffffffff | 0);\n", "import { jsonToConvex } from \"../../values/index.js\";\nimport { Long } from \"../long.js\";\nimport { logForFunction, Logger } from \"../logging.js\";\nimport { QueryId, StateVersion, Transition } from \"./protocol.js\";\nimport { FunctionResult } from \"./function_result.js\";\n\n/**\n * A represention of the query results we've received on the current WebSocket\n * connection.\n *\n * Queries you won't find here include:\n * - queries which have been requested, but no query transition has been received yet for\n * - queries which are populated only though active optimistic updates, but are not subscribed to\n * - queries which have already been removed by the server (which it shouldn't do unless that's\n *   been requested by the client)\n */\nexport class RemoteQuerySet {\n  private version: StateVersion;\n  private readonly remoteQuerySet: Map<QueryId, FunctionResult>;\n  private readonly queryPath: (queryId: QueryId) => string | null;\n  private readonly logger: Logger;\n\n  constructor(queryPath: (queryId: QueryId) => string | null, logger: Logger) {\n    this.version = { querySet: 0, ts: Long.fromNumber(0), identity: 0 };\n    this.remoteQuerySet = new Map();\n    this.queryPath = queryPath;\n    this.logger = logger;\n  }\n\n  transition(transition: Transition): void {\n    const start = transition.startVersion;\n    if (\n      this.version.querySet !== start.querySet ||\n      this.version.ts.notEquals(start.ts) ||\n      this.version.identity !== start.identity\n    ) {\n      throw new Error(\n        `Invalid start version: ${start.ts.toString()}:${start.querySet}`,\n      );\n    }\n    for (const modification of transition.modifications) {\n      switch (modification.type) {\n        case \"QueryUpdated\": {\n          const queryPath = this.queryPath(modification.queryId);\n          if (queryPath) {\n            for (const line of modification.logLines) {\n              logForFunction(this.logger, \"info\", \"query\", queryPath, line);\n            }\n          }\n          const value = jsonToConvex(modification.value ?? null);\n          this.remoteQuerySet.set(modification.queryId, {\n            success: true,\n            value,\n            logLines: modification.logLines,\n          });\n          break;\n        }\n        case \"QueryFailed\": {\n          const queryPath = this.queryPath(modification.queryId);\n          if (queryPath) {\n            for (const line of modification.logLines) {\n              logForFunction(this.logger, \"info\", \"query\", queryPath, line);\n            }\n          }\n          const { errorData } = modification;\n          this.remoteQuerySet.set(modification.queryId, {\n            success: false,\n            errorMessage: modification.errorMessage,\n            errorData:\n              errorData !== undefined ? jsonToConvex(errorData) : undefined,\n            logLines: modification.logLines,\n          });\n          break;\n        }\n        case \"QueryRemoved\": {\n          this.remoteQuerySet.delete(modification.queryId);\n          break;\n        }\n        default: {\n          // Enforce that the switch-case is exhaustive.\n          modification satisfies never;\n          throw new Error(`Invalid modification ${(modification as any).type}`);\n        }\n      }\n    }\n    this.version = transition.endVersion;\n  }\n\n  remoteQueryResults(): Map<QueryId, FunctionResult> {\n    return this.remoteQuerySet;\n  }\n\n  timestamp(): Long {\n    return this.version.ts;\n  }\n}\n", "import type { UserIdentityAttributes } from \"../../server/authentication.js\";\nexport type { UserIdentityAttributes } from \"../../server/authentication.js\";\nimport { JSONValue, Base64 } from \"../../values/index.js\";\nimport { Long } from \"../long.js\";\n\n/**\n * Shared schema\n */\n\nexport function u64ToLong(encoded: EncodedU64): U64 {\n  const integerBytes = Base64.toByteArray(encoded);\n  return Long.fromBytesLE(Array.from(integerBytes));\n}\n\nexport function longToU64(raw: U64): EncodedU64 {\n  const integerBytes = new Uint8Array(raw.toBytesLE());\n  return Base64.fromByteArray(integerBytes);\n}\n\nexport function parseServerMessage(\n  encoded: EncodedServerMessage,\n): ServerMessage {\n  switch (encoded.type) {\n    case \"FatalError\":\n    case \"AuthError\":\n    case \"ActionResponse\":\n    case \"Ping\": {\n      return { ...encoded };\n    }\n    case \"MutationResponse\": {\n      if (encoded.success) {\n        return { ...encoded, ts: u64ToLong(encoded.ts) };\n      } else {\n        return { ...encoded };\n      }\n    }\n    case \"Transition\": {\n      return {\n        ...encoded,\n        startVersion: {\n          ...encoded.startVersion,\n          ts: u64ToLong(encoded.startVersion.ts),\n        },\n        endVersion: {\n          ...encoded.endVersion,\n          ts: u64ToLong(encoded.endVersion.ts),\n        },\n      };\n    }\n    default: {\n      encoded satisfies never;\n    }\n  }\n  return undefined as never;\n}\n\nexport function encodeClientMessage(\n  message: ClientMessage,\n): EncodedClientMessage {\n  switch (message.type) {\n    case \"Authenticate\":\n    case \"ModifyQuerySet\":\n    case \"Mutation\":\n    case \"Action\":\n    case \"Event\": {\n      return { ...message };\n    }\n    case \"Connect\": {\n      if (message.maxObservedTimestamp !== undefined) {\n        return {\n          ...message,\n          maxObservedTimestamp: longToU64(message.maxObservedTimestamp),\n        };\n      } else {\n        return { ...message, maxObservedTimestamp: undefined };\n      }\n    }\n    default: {\n      message satisfies never;\n    }\n  }\n  return undefined as never;\n}\n\ntype U64 = Long;\ntype EncodedU64 = string;\n\n/**\n * Unique nonnegative integer identifying a single query.\n */\nexport type QueryId = number; // nonnegative int\n\nexport type QuerySetVersion = number; // nonnegative int\n\nexport type RequestId = number; // nonnegative int\n\nexport type IdentityVersion = number; // nonnegative int\n\n/**\n * A serialized representation of decisions made during a query's execution.\n *\n * A journal is produced when a query function first executes and is re-used\n * when a query is re-executed.\n *\n * Currently this is used to store pagination end cursors to ensure\n * that pages of paginated queries will always end at the same cursor. This\n * enables gapless, reactive pagination.\n *\n * `null` is used to represent empty journals.\n * @public\n */\nexport type QueryJournal = string | null;\n\n/**\n * Client message schema\n */\n\ntype Connect = {\n  type: \"Connect\";\n  sessionId: string;\n  connectionCount: number;\n  lastCloseReason: string | null;\n  maxObservedTimestamp?: TS;\n};\n\nexport type AddQuery = {\n  type: \"Add\";\n  queryId: QueryId;\n  udfPath: string;\n  args: JSONValue[];\n  journal?: QueryJournal;\n  /**\n   * @internal\n   */\n  componentPath?: string;\n};\n\nexport type RemoveQuery = {\n  type: \"Remove\";\n  queryId: QueryId;\n};\n\nexport type QuerySetModification = {\n  type: \"ModifyQuerySet\";\n  baseVersion: QuerySetVersion;\n  newVersion: QuerySetVersion;\n  modifications: (AddQuery | RemoveQuery)[];\n};\n\nexport type MutationRequest = {\n  type: \"Mutation\";\n  requestId: RequestId;\n  udfPath: string;\n  args: JSONValue[];\n  // Execute the mutation on a specific component.\n  // Only admin auth is allowed to run mutations on non-root components.\n  componentPath?: string;\n};\n\nexport type ActionRequest = {\n  type: \"Action\";\n  requestId: RequestId;\n  udfPath: string;\n  args: JSONValue[];\n  // Execute the action on a specific component.\n  // Only admin auth is allowed to run actions on non-root components.\n  componentPath?: string;\n};\n\nexport type AdminAuthentication = {\n  type: \"Authenticate\";\n  tokenType: \"Admin\";\n  value: string;\n  baseVersion: IdentityVersion;\n  impersonating?: UserIdentityAttributes;\n};\n\nexport type Authenticate =\n  | AdminAuthentication\n  | {\n      type: \"Authenticate\";\n      tokenType: \"User\";\n      value: string;\n      baseVersion: IdentityVersion;\n    }\n  | {\n      type: \"Authenticate\";\n      tokenType: \"None\";\n      baseVersion: IdentityVersion;\n    };\n\nexport type Event = {\n  type: \"Event\";\n  eventType: string;\n  event: any;\n};\nexport type ClientMessage =\n  | Connect\n  | Authenticate\n  | QuerySetModification\n  | MutationRequest\n  | ActionRequest\n  | Event;\n\ntype EncodedConnect = Omit<Connect, \"maxObservedTimestamp\"> & {\n  maxObservedTimestamp?: EncodedTS;\n};\n\ntype EncodedClientMessage =\n  | EncodedConnect\n  | Authenticate\n  | QuerySetModification\n  | MutationRequest\n  | ActionRequest\n  | Event;\n\n/**\n * Server message schema\n */\nexport type TS = U64;\ntype EncodedTS = EncodedU64;\ntype LogLines = string[];\n\nexport type StateVersion = {\n  querySet: QuerySetVersion;\n  ts: TS;\n  identity: IdentityVersion;\n};\ntype EncodedStateVersion = Omit<StateVersion, \"ts\"> & { ts: EncodedTS };\n\ntype StateModification =\n  | {\n      type: \"QueryUpdated\";\n      queryId: QueryId;\n      value: JSONValue;\n      logLines: LogLines;\n      journal: QueryJournal;\n    }\n  | {\n      type: \"QueryFailed\";\n      queryId: QueryId;\n      errorMessage: string;\n      logLines: LogLines;\n      errorData: JSONValue;\n      journal: QueryJournal;\n    }\n  | {\n      type: \"QueryRemoved\";\n      queryId: QueryId;\n    };\n\nexport type Transition = {\n  type: \"Transition\";\n  startVersion: StateVersion;\n  endVersion: StateVersion;\n  modifications: StateModification[];\n};\n\ntype MutationSuccess = {\n  type: \"MutationResponse\";\n  requestId: RequestId;\n  success: true;\n  result: JSONValue;\n  ts: TS;\n  logLines: LogLines;\n};\ntype MutationFailed = {\n  type: \"MutationResponse\";\n  requestId: RequestId;\n  success: false;\n  result: string;\n  logLines: LogLines;\n  errorData?: JSONValue;\n};\nexport type MutationResponse = MutationSuccess | MutationFailed;\ntype ActionSuccess = {\n  type: \"ActionResponse\";\n  requestId: RequestId;\n  success: true;\n  result: JSONValue;\n  logLines: LogLines;\n};\ntype ActionFailed = {\n  type: \"ActionResponse\";\n  requestId: RequestId;\n  success: false;\n  result: string;\n  logLines: LogLines;\n  errorData?: JSONValue;\n};\nexport type ActionResponse = ActionSuccess | ActionFailed;\nexport type AuthError = {\n  type: \"AuthError\";\n  error: string;\n  baseVersion: IdentityVersion;\n  // True if this error is in response to processing a new `Authenticate` message.\n  // Other AuthErrors may occur due to executing a function with expired auth and\n  // should be handled differently.\n  authUpdateAttempted: boolean;\n};\ntype FatalError = {\n  type: \"FatalError\";\n  error: string;\n};\ntype Ping = {\n  type: \"Ping\";\n};\n\nexport type ServerMessage =\n  | Transition\n  | MutationResponse\n  | ActionResponse\n  | FatalError\n  | AuthError\n  | Ping;\n\ntype EncodedTransition = Omit<Transition, \"startVersion\" | \"endVersion\"> & {\n  startVersion: EncodedStateVersion;\n  endVersion: EncodedStateVersion;\n};\ntype EncodedMutationSuccess = Omit<MutationSuccess, \"ts\"> & { ts: EncodedTS };\ntype EncodedMutationResponse = MutationFailed | EncodedMutationSuccess;\n\ntype EncodedServerMessage =\n  | EncodedTransition\n  | EncodedMutationResponse\n  | ActionResponse\n  | FatalError\n  | AuthError\n  | Ping;\n", "import { Logger } from \"../logging.js\";\nimport {\n  ClientMessage,\n  encodeClientMessage,\n  parseServerMessage,\n  ServerMessage,\n} from \"./protocol.js\";\n\nconst CLOSE_NORMAL = 1000;\nconst CLOSE_GOING_AWAY = 1001;\nconst CLOSE_NO_STATUS = 1005;\n/** Convex-specific close code representing a \"404 Not Found\".\n * The edge Onramp accepts websocket upgrades before confirming that the\n * intended destination exists, so this code is sent once we've discovered that\n * the destination does not exist.\n */\nconst CLOSE_NOT_FOUND = 4040;\n\n/**\n * The various states our WebSocket can be in:\n *\n * - \"disconnected\": We don't have a WebSocket, but plan to create one.\n * - \"connecting\": We have created the WebSocket and are waiting for the\n *   `onOpen` callback.\n * - \"ready\": We have an open WebSocket.\n * - \"stopped\": The WebSocket was closed and a new one can be created via `.restart()`.\n * - \"terminated\": We have closed the WebSocket and will never create a new one.\n *\n *\n * WebSocket State Machine\n * -----------------------\n * initialState: disconnected\n * validTransitions:\n *   disconnected:\n *     new WebSocket() -> connecting\n *     terminate() -> terminated\n *   connecting:\n *     onopen -> ready\n *     close() -> disconnected\n *     terminate() -> terminated\n *   ready:\n *     close() -> disconnected\n *     stop() -> stopped\n *     terminate() -> terminated\n *   stopped:\n *     restart() -> connecting\n *     terminate() -> terminated\n * terminalStates:\n *   terminated\n *\n *\n *\n *                                        ┌────────────────┐\n *                ┌────terminate()────────│  disconnected  │◀─┐\n *                │                       └────────────────┘  │\n *                ▼                            │       ▲      │\n *       ┌────────────────┐           new WebSocket()  │      │\n *    ┌─▶│   terminated   │◀──────┐            │       │      │\n *    │  └────────────────┘       │            │       │      │\n *    │           ▲          terminate()       │    close() close()\n *    │      terminate()          │            │       │      │\n *    │           │               │            ▼       │      │\n *    │  ┌────────────────┐       └───────┌────────────────┐  │\n *    │  │    stopped     │──restart()───▶│   connecting   │  │\n *    │  └────────────────┘               └────────────────┘  │\n *    │           ▲                                │          │\n *    │           │                               onopen      │\n *    │           │                                │          │\n *    │           │                                ▼          │\n * terminate()    │                       ┌────────────────┐  │\n *    │           └────────stop()─────────│     ready      │──┘\n *    │                                   └────────────────┘\n *    │                                            │\n *    │                                            │\n *    └────────────────────────────────────────────┘\n *\n * The `connecting` and `ready` state have a sub-state-machine for pausing.\n */\n\ntype Socket =\n  | { state: \"disconnected\" }\n  | { state: \"connecting\"; ws: WebSocket; paused: \"yes\" | \"no\" }\n  | { state: \"ready\"; ws: WebSocket; paused: \"yes\" | \"no\" | \"uninitialized\" }\n  | { state: \"stopped\" }\n  | { state: \"terminated\" };\n\nexport type ReconnectMetadata = {\n  connectionCount: number;\n  lastCloseReason: string | null;\n};\n\nexport type OnMessageResponse = {\n  hasSyncedPastLastReconnect: boolean;\n};\n\nconst serverDisconnectErrors = {\n  // A known error, e.g. during a restart or push\n  InternalServerError: { timeout: 1000 },\n  // ErrorMetadata::overloaded() messages that we realy should back off\n  SubscriptionsWorkerFullError: { timeout: 3000 },\n  TooManyConcurrentRequests: { timeout: 3000 },\n  CommitterFullError: { timeout: 3000 },\n  AwsTooManyRequestsException: { timeout: 3000 },\n  ExecuteFullError: { timeout: 3000 },\n  SystemTimeoutError: { timeout: 3000 },\n  ExpiredInQueue: { timeout: 3000 },\n  // ErrorMetadata::feature_temporarily_unavailable() that typically indicate a deploy just happened\n  VectorIndexesUnavailable: { timeout: 1000 },\n  SearchIndexesUnavailable: { timeout: 1000 },\n  TableSummariesUnavailable: { timeout: 1000 },\n  // More ErrorMeatadata::overloaded()\n  VectorIndexTooLarge: { timeout: 3000 },\n  SearchIndexTooLarge: { timeout: 3000 },\n  TooManyWritesInTimePeriod: { timeout: 3000 },\n} as const satisfies Record<string, { timeout: number }>;\n\ntype ServerDisconnectError = keyof typeof serverDisconnectErrors | \"Unknown\";\n\nfunction classifyDisconnectError(s?: string): ServerDisconnectError {\n  if (s === undefined) return \"Unknown\";\n  // startsWith so more info could be at the end (although currently there isn't)\n\n  for (const prefix of Object.keys(\n    serverDisconnectErrors,\n  ) as ServerDisconnectError[]) {\n    if (s.startsWith(prefix)) {\n      return prefix;\n    }\n  }\n  return \"Unknown\";\n}\n\n/**\n * A wrapper around a websocket that handles errors, reconnection, and message\n * parsing.\n */\nexport class WebSocketManager {\n  private socket: Socket;\n\n  private connectionCount: number;\n  private _hasEverConnected: boolean = false;\n  private lastCloseReason:\n    | \"InitialConnect\"\n    | \"OnCloseInvoked\"\n    | (string & {}) // a full serverErrorReason (not just the prefix) or a new one\n    | null;\n\n  /** Upon HTTPS/WSS failure, the first jittered backoff duration, in ms. */\n  private readonly defaultInitialBackoff: number;\n\n  /** We backoff exponentially, but we need to cap that--this is the jittered max. */\n  private readonly maxBackoff: number;\n\n  /** How many times have we failed consecutively? */\n  private retries: number;\n\n  /** How long before lack of server response causes us to initiate a reconnect,\n   * in ms */\n  private readonly serverInactivityThreshold: number;\n\n  private reconnectDueToServerInactivityTimeout: ReturnType<\n    typeof setTimeout\n  > | null;\n\n  private readonly uri: string;\n  private readonly onOpen: (reconnectMetadata: ReconnectMetadata) => void;\n  private readonly onResume: () => void;\n  private readonly onMessage: (message: ServerMessage) => OnMessageResponse;\n  private readonly webSocketConstructor: typeof WebSocket;\n  private readonly logger: Logger;\n  private readonly onServerDisconnectError:\n    | ((message: string) => void)\n    | undefined;\n\n  constructor(\n    uri: string,\n    callbacks: {\n      onOpen: (reconnectMetadata: ReconnectMetadata) => void;\n      onResume: () => void;\n      onMessage: (message: ServerMessage) => OnMessageResponse;\n      onServerDisconnectError?: (message: string) => void;\n    },\n    webSocketConstructor: typeof WebSocket,\n    logger: Logger,\n    private readonly markConnectionStateDirty: () => void,\n  ) {\n    this.webSocketConstructor = webSocketConstructor;\n    this.socket = { state: \"disconnected\" };\n    this.connectionCount = 0;\n    this.lastCloseReason = \"InitialConnect\";\n\n    // backoff for unknown errors\n    this.defaultInitialBackoff = 1000;\n    this.maxBackoff = 16000;\n    this.retries = 0;\n\n    // Ping messages (sync protocol Pings, not WebSocket protocol Pings) are\n    // sent every 15s in the absence of other messages. But a single large\n    // Transition or other downstream message can hog the line so this\n    // threshold is set higher to prevent clients from giving up.\n    this.serverInactivityThreshold = 60000;\n    this.reconnectDueToServerInactivityTimeout = null;\n\n    this.uri = uri;\n    this.onOpen = callbacks.onOpen;\n    this.onResume = callbacks.onResume;\n    this.onMessage = callbacks.onMessage;\n    this.onServerDisconnectError = callbacks.onServerDisconnectError;\n    this.logger = logger;\n\n    this.connect();\n  }\n\n  private setSocketState(state: Socket) {\n    this.socket = state;\n    this._logVerbose(\n      `socket state changed: ${this.socket.state}, paused: ${\n        \"paused\" in this.socket ? this.socket.paused : undefined\n      }`,\n    );\n    this.markConnectionStateDirty();\n  }\n\n  private connect() {\n    if (this.socket.state === \"terminated\") {\n      return;\n    }\n    if (\n      this.socket.state !== \"disconnected\" &&\n      this.socket.state !== \"stopped\"\n    ) {\n      throw new Error(\n        \"Didn't start connection from disconnected state: \" + this.socket.state,\n      );\n    }\n\n    const ws = new this.webSocketConstructor(this.uri);\n    this._logVerbose(\"constructed WebSocket\");\n    this.setSocketState({\n      state: \"connecting\",\n      ws,\n      paused: \"no\",\n    });\n\n    // Kick off server inactivity timer before WebSocket connection is established\n    // so we can detect cases where handshake fails.\n    // The `onopen` event only fires after the connection is established:\n    // Source: https://datatracker.ietf.org/doc/html/rfc6455#page-19:~:text=_The%20WebSocket%20Connection%20is%20Established_,-and\n    this.resetServerInactivityTimeout();\n\n    ws.onopen = () => {\n      this.logger.logVerbose(\"begin ws.onopen\");\n      if (this.socket.state !== \"connecting\") {\n        throw new Error(\"onopen called with socket not in connecting state\");\n      }\n      this.setSocketState({\n        state: \"ready\",\n        ws,\n        paused: this.socket.paused === \"yes\" ? \"uninitialized\" : \"no\",\n      });\n      this.resetServerInactivityTimeout();\n      if (this.socket.paused === \"no\") {\n        this._hasEverConnected = true;\n        this.onOpen({\n          connectionCount: this.connectionCount,\n          lastCloseReason: this.lastCloseReason,\n        });\n      }\n\n      if (this.lastCloseReason !== \"InitialConnect\") {\n        this.logger.log(\"WebSocket reconnected\");\n      }\n\n      this.connectionCount += 1;\n      this.lastCloseReason = null;\n    };\n    // NB: The WebSocket API calls `onclose` even if connection fails, so we can route all error paths through `onclose`.\n    ws.onerror = (error) => {\n      const message = (error as ErrorEvent).message;\n      this.logger.log(`WebSocket error: ${message}`);\n    };\n    ws.onmessage = (message) => {\n      this.resetServerInactivityTimeout();\n      const serverMessage = parseServerMessage(JSON.parse(message.data));\n      this._logVerbose(`received ws message with type ${serverMessage.type}`);\n      const response = this.onMessage(serverMessage);\n      if (response.hasSyncedPastLastReconnect) {\n        // Reset backoff to 0 once all outstanding requests are complete.\n        this.retries = 0;\n        this.markConnectionStateDirty();\n      }\n    };\n    ws.onclose = (event) => {\n      this._logVerbose(\"begin ws.onclose\");\n      if (this.lastCloseReason === null) {\n        this.lastCloseReason = event.reason ?? \"OnCloseInvoked\";\n      }\n      if (\n        event.code !== CLOSE_NORMAL &&\n        event.code !== CLOSE_GOING_AWAY && // This commonly gets fired on mobile apps when the app is backgrounded\n        event.code !== CLOSE_NO_STATUS &&\n        event.code !== CLOSE_NOT_FOUND // Note that we want to retry on a 404, as it can be transient during a push.\n      ) {\n        let msg = `WebSocket closed with code ${event.code}`;\n        if (event.reason) {\n          msg += `: ${event.reason}`;\n        }\n        this.logger.log(msg);\n        if (this.onServerDisconnectError && event.reason) {\n          // This callback is a unstable API, InternalServerErrors in particular may be removed\n          // since they reflect expected temporary downtime. But until a quantitative measure\n          // of uptime is reported this unstable API errs on the inclusive side.\n          this.onServerDisconnectError(msg);\n        }\n      }\n      const reason = classifyDisconnectError(event.reason);\n      this.scheduleReconnect(reason);\n      return;\n    };\n  }\n\n  /**\n   * @returns The state of the {@link Socket}.\n   */\n  socketState(): string {\n    return this.socket.state;\n  }\n\n  /**\n   * @param message - A ClientMessage to send.\n   * @returns Whether the message (might have been) sent.\n   */\n  sendMessage(message: ClientMessage) {\n    const messageForLog = {\n      type: message.type,\n      ...(message.type === \"Authenticate\" && message.tokenType === \"User\"\n        ? {\n            value: `...${message.value.slice(-7)}`,\n          }\n        : {}),\n    };\n    if (this.socket.state === \"ready\" && this.socket.paused === \"no\") {\n      const encodedMessage = encodeClientMessage(message);\n      const request = JSON.stringify(encodedMessage);\n      try {\n        this.socket.ws.send(request);\n      } catch (error: any) {\n        this.logger.log(\n          `Failed to send message on WebSocket, reconnecting: ${error}`,\n        );\n        this.closeAndReconnect(\"FailedToSendMessage\");\n      }\n      // We are not sure if this was sent or not.\n      this._logVerbose(\n        `sent message with type ${message.type}: ${JSON.stringify(\n          messageForLog,\n        )}`,\n      );\n      return true;\n    }\n    this._logVerbose(\n      `message not sent (socket state: ${this.socket.state}, paused: ${\"paused\" in this.socket ? this.socket.paused : undefined}): ${JSON.stringify(\n        messageForLog,\n      )}`,\n    );\n\n    return false;\n  }\n\n  private resetServerInactivityTimeout() {\n    if (this.socket.state === \"terminated\") {\n      // Don't reset any timers if we were trying to terminate.\n      return;\n    }\n    if (this.reconnectDueToServerInactivityTimeout !== null) {\n      clearTimeout(this.reconnectDueToServerInactivityTimeout);\n      this.reconnectDueToServerInactivityTimeout = null;\n    }\n    this.reconnectDueToServerInactivityTimeout = setTimeout(() => {\n      this.closeAndReconnect(\"InactiveServer\");\n    }, this.serverInactivityThreshold);\n  }\n\n  private scheduleReconnect(reason: \"client\" | ServerDisconnectError) {\n    this.socket = { state: \"disconnected\" };\n    const backoff = this.nextBackoff(reason);\n    this.markConnectionStateDirty();\n    this.logger.log(`Attempting reconnect in ${backoff}ms`);\n    setTimeout(() => this.connect(), backoff);\n  }\n\n  /**\n   * Close the WebSocket and schedule a reconnect.\n   *\n   * This should be used when we hit an error and would like to restart the session.\n   */\n  private closeAndReconnect(closeReason: string) {\n    this._logVerbose(`begin closeAndReconnect with reason ${closeReason}`);\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"terminated\":\n      case \"stopped\":\n        // Nothing to do if we don't have a WebSocket.\n        return;\n      case \"connecting\":\n      case \"ready\": {\n        this.lastCloseReason = closeReason;\n        // Close the old socket asynchronously, we'll open a new socket in reconnect.\n        void this.close();\n        this.scheduleReconnect(\"client\");\n        return;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n  }\n\n  /**\n   * Close the WebSocket, being careful to clear the onclose handler to avoid re-entrant\n   * calls. Use this instead of directly calling `ws.close()`\n   *\n   * It is the callers responsibility to update the state after this method is called so that the\n   * closed socket is not accessible or used again after this method is called\n   */\n  private close(): Promise<void> {\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"terminated\":\n      case \"stopped\":\n        // Nothing to do if we don't have a WebSocket.\n        return Promise.resolve();\n      case \"connecting\": {\n        const ws = this.socket.ws;\n        return new Promise((r) => {\n          ws.onclose = () => {\n            this._logVerbose(\"Closed after connecting\");\n            r();\n          };\n          ws.onopen = () => {\n            this._logVerbose(\"Opened after connecting\");\n            ws.close();\n          };\n        });\n      }\n      case \"ready\": {\n        this._logVerbose(\"ws.close called\");\n        const ws = this.socket.ws;\n        const result: Promise<void> = new Promise((r) => {\n          ws.onclose = () => {\n            r();\n          };\n        });\n        ws.close();\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return Promise.resolve();\n      }\n    }\n  }\n\n  /**\n   * Close the WebSocket and do not reconnect.\n   * @returns A Promise that resolves when the WebSocket `onClose` callback is called.\n   */\n  terminate(): Promise<void> {\n    if (this.reconnectDueToServerInactivityTimeout) {\n      clearTimeout(this.reconnectDueToServerInactivityTimeout);\n    }\n    switch (this.socket.state) {\n      case \"terminated\":\n      case \"stopped\":\n      case \"disconnected\":\n      case \"connecting\":\n      case \"ready\": {\n        const result = this.close();\n        this.setSocketState({ state: \"terminated\" });\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        throw new Error(\n          `Invalid websocket state: ${(this.socket as any).state}`,\n        );\n      }\n    }\n  }\n\n  stop(): Promise<void> {\n    switch (this.socket.state) {\n      case \"terminated\":\n        // If we're terminating we ignore stop\n        return Promise.resolve();\n      case \"connecting\":\n      case \"stopped\":\n      case \"disconnected\":\n      case \"ready\": {\n        const result = this.close();\n        this.socket = { state: \"stopped\" };\n        return result;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return Promise.resolve();\n      }\n    }\n  }\n\n  /**\n   * Create a new WebSocket after a previous `stop()`, unless `terminate()` was\n   * called before.\n   */\n  tryRestart(): void {\n    switch (this.socket.state) {\n      case \"stopped\":\n        break;\n      case \"terminated\":\n      case \"connecting\":\n      case \"ready\":\n      case \"disconnected\":\n        this.logger.logVerbose(\"Restart called without stopping first\");\n        return;\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n    this.connect();\n  }\n\n  pause(): void {\n    switch (this.socket.state) {\n      case \"disconnected\":\n      case \"stopped\":\n      case \"terminated\":\n        // If already stopped or stopping ignore.\n        return;\n      case \"connecting\":\n      case \"ready\": {\n        this.socket = { ...this.socket, paused: \"yes\" };\n        return;\n      }\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n        return;\n      }\n    }\n  }\n\n  /**\n   * Resume the state machine if previously paused.\n   */\n  resume(): void {\n    switch (this.socket.state) {\n      case \"connecting\":\n        this.socket = { ...this.socket, paused: \"no\" };\n        return;\n      case \"ready\":\n        if (this.socket.paused === \"uninitialized\") {\n          this.socket = { ...this.socket, paused: \"no\" };\n          this.onOpen({\n            connectionCount: this.connectionCount,\n            lastCloseReason: this.lastCloseReason,\n          });\n        } else if (this.socket.paused === \"yes\") {\n          this.socket = { ...this.socket, paused: \"no\" };\n          this.onResume();\n        }\n        return;\n      case \"terminated\":\n      case \"stopped\":\n      case \"disconnected\":\n        // Ignore resume if not paused, perhaps we already resumed.\n        return;\n      default: {\n        // Enforce that the switch-case is exhaustive.\n        this.socket satisfies never;\n      }\n    }\n    this.connect();\n  }\n\n  connectionState(): {\n    isConnected: boolean;\n    hasEverConnected: boolean;\n    connectionCount: number;\n    connectionRetries: number;\n  } {\n    return {\n      isConnected: this.socket.state === \"ready\",\n      hasEverConnected: this._hasEverConnected,\n      connectionCount: this.connectionCount,\n      connectionRetries: this.retries,\n    };\n  }\n\n  private _logVerbose(message: string) {\n    this.logger.logVerbose(message);\n  }\n\n  private nextBackoff(reason: \"client\" | ServerDisconnectError): number {\n    const initialBackoff: number =\n      reason === \"client\"\n        ? 100 // There's no evidence of a server problem, retry quickly\n        : reason === \"Unknown\"\n          ? this.defaultInitialBackoff\n          : serverDisconnectErrors[reason].timeout;\n\n    const baseBackoff = initialBackoff * Math.pow(2, this.retries);\n    this.retries += 1;\n    const actualBackoff = Math.min(baseBackoff, this.maxBackoff);\n    const jitter = actualBackoff * (Math.random() - 0.5);\n    return actualBackoff + jitter;\n  }\n}\n", "export function newSessionId() {\n  return uuidv4();\n}\n\n// From https://stackoverflow.com/a/2117523\nfunction uuidv4() {\n  return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16) | 0,\n      v = c === \"x\" ? r : (r & 0x3) | 0x8;\n    return v.toString(16);\n  });\n}\n", "export class InvalidTokenError extends Error {\n}\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {\n        let code = p.charCodeAt(0).toString(16).toUpperCase();\n        if (code.length < 2) {\n            code = \"0\" + code;\n        }\n        return \"%\" + code;\n    }));\n}\nfunction base64UrlDecode(str) {\n    let output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw new Error(\"base64 string is not of the correct length\");\n    }\n    try {\n        return b64DecodeUnicode(output);\n    }\n    catch (err) {\n        return atob(output);\n    }\n}\nexport function jwtDecode(token, options) {\n    if (typeof token !== \"string\") {\n        throw new InvalidTokenError(\"Invalid token specified: must be a string\");\n    }\n    options || (options = {});\n    const pos = options.header === true ? 0 : 1;\n    const part = token.split(\".\")[pos];\n    if (typeof part !== \"string\") {\n        throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);\n    }\n    let decoded;\n    try {\n        decoded = base64UrlDecode(part);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);\n    }\n    try {\n        return JSON.parse(decoded);\n    }\n    catch (e) {\n        throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);\n    }\n}\n", "import { Logger } from \"../logging.js\";\nimport { LocalSyncState } from \"./local_state.js\";\nimport { AuthError, IdentityVersion, Transition } from \"./protocol.js\";\nimport { jwtDecode } from \"jwt-decode\";\n\n// setTimout uses 32 bit integer, so it can only\n// schedule about 24 days in the future.\nconst MAXIMUM_REFRESH_DELAY = 20 * 24 * 60 * 60 * 1000; // 20 days\n\nconst MAX_TOKEN_CONFIRMATION_ATTEMPTS = 2;\n\n/**\n * An async function returning a JWT. Depending on the auth providers\n * configured in convex/auth.config.ts, this may be a JWT-encoded OpenID\n * Connect Identity Token or a traditional JWT.\n *\n * `forceRefreshToken` is `true` if the server rejected a previously\n * returned token or the token is anticipated to expiring soon\n * based on its `exp` time.\n *\n * See {@link ConvexReactClient.setAuth}.\n *\n * @public\n */\nexport type AuthTokenFetcher = (args: {\n  forceRefreshToken: boolean;\n}) => Promise<string | null | undefined>;\n\n/**\n * What is provided to the client.\n */\ntype AuthConfig = {\n  fetchToken: AuthTokenFetcher;\n  onAuthChange: (isAuthenticated: boolean) => void;\n};\n\n/**\n * In general we take 3 steps:\n *   1. Fetch a possibly cached token\n *   2. Immediately fetch a fresh token without using a cache\n *   3. Repeat step 2 before the end of the fresh token's lifetime\n *\n * When we fetch without using a cache we know when the token\n * will expire, and can schedule refetching it.\n *\n * If we get an error before a scheduled refetch, we go back\n * to step 2.\n */\ntype AuthState =\n  | { state: \"noAuth\" }\n  | {\n      state: \"waitingForServerConfirmationOfCachedToken\";\n      config: AuthConfig;\n      hasRetried: boolean;\n    }\n  | {\n      state: \"initialRefetch\";\n      config: AuthConfig;\n    }\n  | {\n      state: \"waitingForServerConfirmationOfFreshToken\";\n      config: AuthConfig;\n      hadAuth: boolean;\n      token: string;\n    }\n  | {\n      state: \"waitingForScheduledRefetch\";\n      config: AuthConfig;\n      refetchTokenTimeoutId: ReturnType<typeof setTimeout>;\n    }\n  // Special/weird state when we got a valid token\n  // but could not fetch a new one.\n  | {\n      state: \"notRefetching\";\n      config: AuthConfig;\n    };\n\n/**\n * Handles the state transitions for auth. The server is the source\n * of truth.\n */\nexport class AuthenticationManager {\n  private authState: AuthState = { state: \"noAuth\" };\n  // Used to detect races involving `setConfig` calls\n  // while a token is being fetched.\n  private configVersion = 0;\n  // Shared by the BaseClient so that the auth manager can easily inspect it\n  private readonly syncState: LocalSyncState;\n  // Passed down by BaseClient, sends a message to the server\n  private readonly authenticate: (token: string) => IdentityVersion;\n  private readonly stopSocket: () => Promise<void>;\n  private readonly tryRestartSocket: () => void;\n  private readonly pauseSocket: () => void;\n  private readonly resumeSocket: () => void;\n  // Passed down by BaseClient, sends a message to the server\n  private readonly clearAuth: () => void;\n  private readonly logger: Logger;\n  private readonly refreshTokenLeewaySeconds: number;\n  // Number of times we have attempted to confirm the latest token. We retry up\n  // to `MAX_TOKEN_CONFIRMATION_ATTEMPTS` times.\n  private tokenConfirmationAttempts = 0;\n  constructor(\n    syncState: LocalSyncState,\n    callbacks: {\n      authenticate: (token: string) => IdentityVersion;\n      stopSocket: () => Promise<void>;\n      tryRestartSocket: () => void;\n      pauseSocket: () => void;\n      resumeSocket: () => void;\n      clearAuth: () => void;\n    },\n    config: {\n      refreshTokenLeewaySeconds: number;\n      logger: Logger;\n    },\n  ) {\n    this.syncState = syncState;\n    this.authenticate = callbacks.authenticate;\n    this.stopSocket = callbacks.stopSocket;\n    this.tryRestartSocket = callbacks.tryRestartSocket;\n    this.pauseSocket = callbacks.pauseSocket;\n    this.resumeSocket = callbacks.resumeSocket;\n    this.clearAuth = callbacks.clearAuth;\n    this.logger = config.logger;\n    this.refreshTokenLeewaySeconds = config.refreshTokenLeewaySeconds;\n  }\n\n  async setConfig(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ) {\n    this.resetAuthState();\n    this._logVerbose(\"pausing WS for auth token fetch\");\n    this.pauseSocket();\n    const token = await this.fetchTokenAndGuardAgainstRace(fetchToken, {\n      forceRefreshToken: false,\n    });\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n    if (token.value) {\n      this.setAuthState({\n        state: \"waitingForServerConfirmationOfCachedToken\",\n        config: { fetchToken, onAuthChange: onChange },\n        hasRetried: false,\n      });\n      this.authenticate(token.value);\n    } else {\n      this.setAuthState({\n        state: \"initialRefetch\",\n        config: { fetchToken, onAuthChange: onChange },\n      });\n      // Try again with `forceRefreshToken: true`\n      await this.refetchToken();\n    }\n    this._logVerbose(\"resuming WS after auth token fetch\");\n    this.resumeSocket();\n  }\n\n  onTransition(serverMessage: Transition) {\n    if (\n      !this.syncState.isCurrentOrNewerAuthVersion(\n        serverMessage.endVersion.identity,\n      )\n    ) {\n      // This is a stale transition - client has moved on to\n      // a newer auth version.\n      return;\n    }\n    if (\n      serverMessage.endVersion.identity <= serverMessage.startVersion.identity\n    ) {\n      // This transition did not change auth - it is not a response to Authenticate.\n      return;\n    }\n\n    if (this.authState.state === \"waitingForServerConfirmationOfCachedToken\") {\n      this._logVerbose(\"server confirmed auth token is valid\");\n      void this.refetchToken();\n      this.authState.config.onAuthChange(true);\n      return;\n    }\n    if (this.authState.state === \"waitingForServerConfirmationOfFreshToken\") {\n      this._logVerbose(\"server confirmed new auth token is valid\");\n      this.scheduleTokenRefetch(this.authState.token);\n      this.tokenConfirmationAttempts = 0;\n      if (!this.authState.hadAuth) {\n        this.authState.config.onAuthChange(true);\n      }\n    }\n  }\n\n  onAuthError(serverMessage: AuthError) {\n    // If the AuthError is not due to updating the token, and we're currently\n    // waiting on the result of a token update, ignore.\n    if (\n      serverMessage.authUpdateAttempted === false &&\n      (this.authState.state === \"waitingForServerConfirmationOfFreshToken\" ||\n        this.authState.state === \"waitingForServerConfirmationOfCachedToken\")\n    ) {\n      this._logVerbose(\"ignoring non-auth token expired error\");\n      return;\n    }\n    const { baseVersion } = serverMessage;\n    // Versioned AuthErrors are ignored if the client advanced to\n    // a newer auth identity\n    // Error are reporting the previous version, since the server\n    // didn't advance, hence `+ 1`.\n    if (!this.syncState.isCurrentOrNewerAuthVersion(baseVersion + 1)) {\n      this._logVerbose(\"ignoring auth error for previous auth attempt\");\n      return;\n    }\n    void this.tryToReauthenticate(serverMessage);\n    return;\n  }\n\n  // This is similar to `refetchToken` defined below, in fact we\n  // don't represent them as different states, but it is different\n  // in that we pause the WebSocket so that mutations\n  // don't retry with bad auth.\n  private async tryToReauthenticate(serverMessage: AuthError) {\n    this._logVerbose(`attempting to reauthenticate: ${serverMessage.error}`);\n    if (\n      // No way to fetch another token, kaboom\n      this.authState.state === \"noAuth\" ||\n      // We failed on a fresh token. After a small number of retries, we give up\n      // and clear the auth state to avoid infinite retries.\n      (this.authState.state === \"waitingForServerConfirmationOfFreshToken\" &&\n        this.tokenConfirmationAttempts >= MAX_TOKEN_CONFIRMATION_ATTEMPTS)\n    ) {\n      this.logger.error(\n        `Failed to authenticate: \"${serverMessage.error}\", check your server auth config`,\n      );\n      if (this.syncState.hasAuth()) {\n        this.syncState.clearAuth();\n      }\n      if (this.authState.state !== \"noAuth\") {\n        this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n      }\n      return;\n    }\n    if (this.authState.state === \"waitingForServerConfirmationOfFreshToken\") {\n      this.tokenConfirmationAttempts++;\n      this._logVerbose(\n        `retrying reauthentication, ${MAX_TOKEN_CONFIRMATION_ATTEMPTS - this.tokenConfirmationAttempts} attempts remaining`,\n      );\n    }\n\n    await this.stopSocket();\n    const token = await this.fetchTokenAndGuardAgainstRace(\n      this.authState.config.fetchToken,\n      {\n        forceRefreshToken: true,\n      },\n    );\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n\n    if (token.value && this.syncState.isNewAuth(token.value)) {\n      this.authenticate(token.value);\n      this.setAuthState({\n        state: \"waitingForServerConfirmationOfFreshToken\",\n        config: this.authState.config,\n        token: token.value,\n        hadAuth:\n          this.authState.state === \"notRefetching\" ||\n          this.authState.state === \"waitingForScheduledRefetch\",\n      });\n    } else {\n      this._logVerbose(\"reauthentication failed, could not fetch a new token\");\n      if (this.syncState.hasAuth()) {\n        this.syncState.clearAuth();\n      }\n      this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n    }\n    this.tryRestartSocket();\n  }\n\n  // Force refetch the token and schedule another refetch\n  // before the token expires - an active client should never\n  // need to reauthenticate.\n  private async refetchToken() {\n    if (this.authState.state === \"noAuth\") {\n      return;\n    }\n    this._logVerbose(\"refetching auth token\");\n    const token = await this.fetchTokenAndGuardAgainstRace(\n      this.authState.config.fetchToken,\n      {\n        forceRefreshToken: true,\n      },\n    );\n    if (token.isFromOutdatedConfig) {\n      return;\n    }\n\n    if (token.value) {\n      if (this.syncState.isNewAuth(token.value)) {\n        this.setAuthState({\n          state: \"waitingForServerConfirmationOfFreshToken\",\n          hadAuth: this.syncState.hasAuth(),\n          token: token.value,\n          config: this.authState.config,\n        });\n        this.authenticate(token.value);\n      } else {\n        this.setAuthState({\n          state: \"notRefetching\",\n          config: this.authState.config,\n        });\n      }\n    } else {\n      this._logVerbose(\"refetching token failed\");\n      if (this.syncState.hasAuth()) {\n        this.clearAuth();\n      }\n      this.setAndReportAuthFailed(this.authState.config.onAuthChange);\n    }\n    // Restart in case this refetch was triggered via schedule during\n    // a reauthentication attempt.\n    this._logVerbose(\n      \"restarting WS after auth token fetch (if currently stopped)\",\n    );\n    this.tryRestartSocket();\n  }\n\n  private scheduleTokenRefetch(token: string) {\n    if (this.authState.state === \"noAuth\") {\n      return;\n    }\n    const decodedToken = this.decodeToken(token);\n    if (!decodedToken) {\n      // This is no longer really possible, because\n      // we wait on server response before scheduling token refetch,\n      // and the server currently requires JWT tokens.\n      this.logger.error(\n        \"Auth token is not a valid JWT, cannot refetch the token\",\n      );\n      return;\n    }\n    // iat: issued at time, UTC seconds timestamp at which the JWT was issued\n    // exp: expiration time, UTC seconds timestamp at which the JWT will expire\n    const { iat, exp } = decodedToken as { iat?: number; exp?: number };\n    if (!iat || !exp) {\n      this.logger.error(\n        \"Auth token does not have required fields, cannot refetch the token\",\n      );\n      return;\n    }\n    // Because the client and server clocks may be out of sync,\n    // we only know that the token will expire after `exp - iat`,\n    // and since we just fetched a fresh one we know when that\n    // will happen.\n    const tokenValiditySeconds = exp - iat;\n    if (tokenValiditySeconds <= 2) {\n      this.logger.error(\n        \"Auth token does not live long enough, cannot refetch the token\",\n      );\n      return;\n    }\n    // Attempt to refresh the token `refreshTokenLeewaySeconds` before it expires,\n    // or immediately if the token is already expiring soon.\n    let delay = Math.min(\n      MAXIMUM_REFRESH_DELAY,\n      (tokenValiditySeconds - this.refreshTokenLeewaySeconds) * 1000,\n    );\n    if (delay <= 0) {\n      // Refetch immediately, but this might be due to configuring a `refreshTokenLeewaySeconds`\n      // that is too large compared to the token's actual lifetime.\n      this.logger.warn(\n        `Refetching auth token immediately, configured leeway ${this.refreshTokenLeewaySeconds}s is larger than the token's lifetime ${tokenValiditySeconds}s`,\n      );\n      delay = 0;\n    }\n    const refetchTokenTimeoutId = setTimeout(() => {\n      this._logVerbose(\"running scheduled token refetch\");\n      void this.refetchToken();\n    }, delay);\n    this.setAuthState({\n      state: \"waitingForScheduledRefetch\",\n      refetchTokenTimeoutId,\n      config: this.authState.config,\n    });\n    this._logVerbose(\n      `scheduled preemptive auth token refetching in ${delay}ms`,\n    );\n  }\n\n  // Protects against simultaneous calls to `setConfig`\n  // while we're fetching a token\n  private async fetchTokenAndGuardAgainstRace(\n    fetchToken: AuthTokenFetcher,\n    fetchArgs: {\n      forceRefreshToken: boolean;\n    },\n  ) {\n    const originalConfigVersion = ++this.configVersion;\n    this._logVerbose(\n      `fetching token with config version ${originalConfigVersion}`,\n    );\n    const token = await fetchToken(fetchArgs);\n    if (this.configVersion !== originalConfigVersion) {\n      // This is a stale config\n      this._logVerbose(\n        `stale config version, expected ${originalConfigVersion}, got ${this.configVersion}`,\n      );\n      return { isFromOutdatedConfig: true };\n    }\n    return { isFromOutdatedConfig: false, value: token };\n  }\n\n  stop() {\n    this.resetAuthState();\n    // Bump this in case we are mid-token-fetch when we get stopped\n    this.configVersion++;\n    this._logVerbose(`config version bumped to ${this.configVersion}`);\n  }\n\n  private setAndReportAuthFailed(\n    onAuthChange: (authenticated: boolean) => void,\n  ) {\n    onAuthChange(false);\n    this.resetAuthState();\n  }\n\n  private resetAuthState() {\n    this.setAuthState({ state: \"noAuth\" });\n  }\n\n  private setAuthState(newAuth: AuthState) {\n    const authStateForLog =\n      newAuth.state === \"waitingForServerConfirmationOfFreshToken\"\n        ? {\n            hadAuth: newAuth.hadAuth,\n            state: newAuth.state,\n            token: `...${newAuth.token.slice(-7)}`,\n          }\n        : { state: newAuth.state };\n    this._logVerbose(\n      `setting auth state to ${JSON.stringify(authStateForLog)}`,\n    );\n    switch (newAuth.state) {\n      case \"waitingForScheduledRefetch\":\n      case \"notRefetching\":\n      case \"noAuth\":\n        this.tokenConfirmationAttempts = 0;\n        break;\n      case \"waitingForServerConfirmationOfFreshToken\":\n      case \"waitingForServerConfirmationOfCachedToken\":\n      case \"initialRefetch\":\n        break;\n      default: {\n        newAuth satisfies never;\n      }\n    }\n    if (this.authState.state === \"waitingForScheduledRefetch\") {\n      clearTimeout(this.authState.refetchTokenTimeoutId);\n\n      // The waitingForScheduledRefetch state is the most quiesced authed state.\n      // Let the syncState know that auth is in a good state, so it can reset failure backoffs\n      this.syncState.markAuthCompletion();\n    }\n    this.authState = newAuth;\n  }\n\n  private decodeToken(token: string) {\n    try {\n      return jwtDecode(token);\n    } catch (e) {\n      this._logVerbose(\n        `Error decoding token: ${e instanceof Error ? e.message : \"Unknown error\"}`,\n      );\n      return null;\n    }\n  }\n\n  private _logVerbose(message: string) {\n    this.logger.logVerbose(`${message} [v${this.configVersion}]`);\n  }\n}\n", "// Marks share a global namespace with other developer code.\nconst markNames = [\n  \"convexClientConstructed\",\n  \"convexWebSocketOpen\",\n  \"convexFirstMessageReceived\",\n] as const;\nexport type MarkName = (typeof markNames)[number];\n\n// Mark details are not reported to the server.\ntype MarkDetail = {\n  sessionId: string;\n};\n\n// `PerformanceMark`s are efficient and show up in browser's performance\n// timeline. They can be cleared with `performance.clearMarks()`.\n// This is a memory leak, but a worthwhile one: automatic\n// cleanup would make in-browser debugging more difficult.\nexport function mark(name: MarkName, sessionId: string) {\n  const detail: MarkDetail = { sessionId };\n  // `performance` APIs exists in browsers, Node.js, Deno, and more but it\n  // is not required by the Convex client.\n  if (typeof performance === \"undefined\" || !performance.mark) return;\n  performance.mark(name, { detail });\n}\n\n// `PerfomanceMark` has a built-in toJSON() but the return type varies\n// between implementations, e.g. Node.js returns details but Chrome does not.\nfunction performanceMarkToJson(mark: PerformanceMark): Mark<PERSON><PERSON> {\n  // Remove \"convex\" prefix\n  let name = mark.name.slice(\"convex\".length);\n  // lowercase the first letter\n  name = name.charAt(0).toLowerCase() + name.slice(1);\n  return {\n    name,\n    startTime: mark.startTime,\n  };\n}\n\n// Similar to the return type of `PerformanceMark.toJson()`.\nexport type MarkJson = {\n  name: string;\n  // `startTime` is in milliseconds since the time origin like `performance.now()`.\n  // https://developer.mozilla.org/en-US/docs/Web/API/DOMHighResTimeStamp#the_time_origin\n  startTime: number;\n};\n\nexport function getMarksReport(sessionId: string): MarkJson[] {\n  if (typeof performance === \"undefined\" || !performance.getEntriesByName) {\n    return [];\n  }\n  const allMarks: PerformanceMark[] = [];\n  for (const name of markNames) {\n    const marks = (\n      performance\n        .getEntriesByName(name)\n        .filter((entry) => entry.entryType === \"mark\") as PerformanceMark[]\n    ).filter((mark) => mark.detail.sessionId === sessionId);\n    allMarks.push(...marks);\n  }\n  return allMarks.map(performanceMarkToJson);\n}\n", "import { version } from \"../../index.js\";\nimport { convexToJson, Value } from \"../../values/index.js\";\nimport {\n  createHybridErrorStacktrace,\n  forwardData,\n  instantiateDefaultLogger,\n  instantiateNoopLogger,\n  logFatalError,\n  Logger,\n} from \"../logging.js\";\nimport { LocalSyncState } from \"./local_state.js\";\nimport { RequestManager } from \"./request_manager.js\";\nimport {\n  OptimisticLocalStore,\n  OptimisticUpdate,\n} from \"./optimistic_updates.js\";\nimport {\n  OptimisticQueryResults,\n  QueryResultsMap,\n} from \"./optimistic_updates_impl.js\";\nimport {\n  ActionRequest,\n  MutationRequest,\n  QueryId,\n  QueryJournal,\n  RequestId,\n  ServerMessage,\n  TS,\n  UserIdentityAttributes,\n} from \"./protocol.js\";\nimport { RemoteQuerySet } from \"./remote_query_set.js\";\nimport { QueryToken, serializePathAndArgs } from \"./udf_path_utils.js\";\nimport { ReconnectMetadata, WebSocketManager } from \"./web_socket_manager.js\";\nimport { newSessionId } from \"./session.js\";\nimport { FunctionResult } from \"./function_result.js\";\nimport {\n  AuthenticationManager,\n  AuthTokenFetcher,\n} from \"./authentication_manager.js\";\nexport { type AuthTokenFetcher } from \"./authentication_manager.js\";\nimport { getMarksReport, mark, MarkName } from \"./metrics.js\";\nimport { parseArgs, validateDeploymentUrl } from \"../../common/index.js\";\nimport { ConvexError } from \"../../values/errors.js\";\n\n/**\n * Options for {@link BaseConvexClient}.\n *\n * @public\n */\nexport interface BaseConvexClientOptions {\n  /**\n   * Whether to prompt the user if they have unsaved changes pending\n   * when navigating away or closing a web page.\n   *\n   * This is only possible when the `window` object exists, i.e. in a browser.\n   *\n   * The default value is `true` in browsers.\n   */\n  unsavedChangesWarning?: boolean;\n  /**\n   * Specifies an alternate\n   * [WebSocket](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)\n   * constructor to use for client communication with the Convex cloud.\n   * The default behavior is to use `WebSocket` from the global environment.\n   */\n  webSocketConstructor?: typeof WebSocket;\n  /**\n   * Adds additional logging for debugging purposes.\n   *\n   * The default value is `false`.\n   */\n  verbose?: boolean;\n  /**\n   * A logger, `true`, or `false`. If not provided or `true`, logs to the console.\n   * If `false`, logs are not printed anywhere.\n   *\n   * You can construct your own logger to customize logging to log elsewhere.\n   * A logger is an object with 4 methods: log(), warn(), error(), and logVerbose().\n   * These methods can receive multiple arguments of any types, like console.log().\n   */\n  logger?: Logger | boolean;\n  /**\n   * Sends additional metrics to Convex for debugging purposes.\n   *\n   * The default value is `false`.\n   */\n  reportDebugInfoToConvex?: boolean;\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * A function to call on receiving abnormal WebSocket close messages from the\n   * connected Convex deployment. The content of these messages is not stable,\n   * it is an implementation detail that may change.\n   *\n   * Consider this API an observability stopgap until higher level codes with\n   * recommendations on what to do are available, which could be a more stable\n   * interface instead of `string`.\n   *\n   * Check `connectionState` for more quantitative metrics about connection status.\n   */\n  onServerDisconnectError?: (message: string) => void;\n  /**\n   * Skip validating that the Convex deployment URL looks like\n   * `https://happy-animal-123.convex.cloud` or localhost.\n   *\n   * This can be useful if running a self-hosted Convex backend that uses a different\n   * URL.\n   *\n   * The default value is `false`\n   */\n  skipConvexDeploymentUrlCheck?: boolean;\n  /**\n   * If using auth, the number of seconds before a token expires that we should refresh it.\n   *\n   * The default value is `2`.\n   */\n  authRefreshTokenLeewaySeconds?: number;\n  /**\n   * This API is experimental: it may change or disappear.\n   *\n   * Whether query, mutation, and action requests should be held back\n   * until the first auth token can be sent.\n   *\n   * Opting into this behavior works well for pages that should\n   * only be viewed by authenticated clients.\n   *\n   * Defaults to false, not waiting for an auth token.\n   */\n  expectAuth?: boolean;\n}\n\n/**\n * State describing the client's connection with the Convex backend.\n *\n * @public\n */\nexport type ConnectionState = {\n  hasInflightRequests: boolean;\n  isWebSocketConnected: boolean;\n  timeOfOldestInflightRequest: Date | null;\n  /**\n   * True if the client has ever opened a WebSocket to the \"ready\" state.\n   */\n  hasEverConnected: boolean;\n  /**\n   * The number of times this client has connected to the Convex backend.\n   *\n   * A number of things can cause the client to reconnect -- server errors,\n   * bad internet, auth expiring. But this number being high is an indication\n   * that the client is having trouble keeping a stable connection.\n   */\n  connectionCount: number;\n  /**\n   * The number of times this client has tried (and failed) to connect to the Convex backend.\n   */\n  connectionRetries: number;\n  /**\n   * The number of mutations currently in flight.\n   */\n  inflightMutations: number;\n  /**\n   * The number of actions currently in flight.\n   */\n  inflightActions: number;\n};\n\n/**\n * Options for {@link BaseConvexClient.subscribe}.\n *\n * @public\n */\nexport interface SubscribeOptions {\n  /**\n   * An (optional) journal produced from a previous execution of this query\n   * function.\n   *\n   * If there is an existing subscription to a query function with the same\n   * name and arguments, this journal will have no effect.\n   */\n  journal?: QueryJournal;\n\n  /**\n   * @internal\n   */\n  componentPath?: string;\n}\n\n/**\n * Options for {@link BaseConvexClient.mutation}.\n *\n * @public\n */\nexport interface MutationOptions {\n  /**\n   * An optimistic update to apply along with this mutation.\n   *\n   * An optimistic update locally updates queries while a mutation is pending.\n   * Once the mutation completes, the update will be rolled back.\n   */\n  optimisticUpdate?: OptimisticUpdate<any>;\n}\n\n/**\n * Type describing updates to a query within a `Transition`.\n *\n * @public\n */\nexport type QueryModification =\n  // `undefined` generally comes from an optimistic update setting the query to be loading\n  { kind: \"Updated\"; result: FunctionResult | undefined } | { kind: \"Removed\" };\n\n/**\n * Object describing a transition passed into the `onTransition` handler.\n *\n * These can be from receiving a transition from the server, or from applying an\n * optimistic update locally.\n *\n * @public\n */\nexport type Transition = {\n  queries: Array<{ token: QueryToken; modification: QueryModification }>;\n  reflectedMutations: Array<{ requestId: RequestId; result: FunctionResult }>;\n  timestamp: TS;\n};\n\n/**\n * Low-level client for directly integrating state management libraries\n * with Convex.\n *\n * Most developers should use higher level clients, like\n * the {@link ConvexHttpClient} or the React hook based {@link react.ConvexReactClient}.\n *\n * @public\n */\nexport class BaseConvexClient {\n  private readonly address: string;\n  private readonly state: LocalSyncState;\n  private readonly requestManager: RequestManager;\n  private readonly webSocketManager: WebSocketManager;\n  private readonly authenticationManager: AuthenticationManager;\n  private remoteQuerySet: RemoteQuerySet;\n  private readonly optimisticQueryResults: OptimisticQueryResults;\n  private _transitionHandlerCounter = 0;\n  private _nextRequestId: RequestId;\n  private _onTransitionFns: Map<number, (transition: Transition) => void> =\n    new Map();\n  private readonly _sessionId: string;\n  private firstMessageReceived = false;\n  private readonly debug: boolean;\n  private readonly logger: Logger;\n  private maxObservedTimestamp: TS | undefined;\n  private connectionStateSubscribers = new Map<\n    number,\n    (connectionState: ConnectionState) => void\n  >();\n  private nextConnectionStateSubscriberId: number = 0;\n  private _lastPublishedConnectionState: ConnectionState | undefined;\n\n  /**\n   * @param address - The url of your Convex deployment, often provided\n   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.\n   * @param onTransition - A callback receiving an array of query tokens\n   * corresponding to query results that have changed -- additional handlers\n   * can be added via `addOnTransitionHandler`.\n   * @param options - See {@link BaseConvexClientOptions} for a full description.\n   */\n  constructor(\n    address: string,\n    onTransition: (updatedQueries: QueryToken[]) => void,\n    options?: BaseConvexClientOptions,\n  ) {\n    if (typeof address === \"object\") {\n      throw new Error(\n        \"Passing a ClientConfig object is no longer supported. Pass the URL of the Convex deployment as a string directly.\",\n      );\n    }\n    if (options?.skipConvexDeploymentUrlCheck !== true) {\n      validateDeploymentUrl(address);\n    }\n    options = { ...options };\n    const authRefreshTokenLeewaySeconds =\n      options.authRefreshTokenLeewaySeconds ?? 2;\n    let webSocketConstructor = options.webSocketConstructor;\n    if (!webSocketConstructor && typeof WebSocket === \"undefined\") {\n      throw new Error(\n        \"No WebSocket global variable defined! To use Convex in an environment without WebSocket try the HTTP client: https://docs.convex.dev/api/classes/browser.ConvexHttpClient\",\n      );\n    }\n    webSocketConstructor = webSocketConstructor || WebSocket;\n    this.debug = options.reportDebugInfoToConvex ?? false;\n    this.address = address;\n    this.logger =\n      options.logger === false\n        ? instantiateNoopLogger({ verbose: options.verbose ?? false })\n        : options.logger !== true && options.logger\n          ? options.logger\n          : instantiateDefaultLogger({ verbose: options.verbose ?? false });\n    // Substitute http(s) with ws(s)\n    const i = address.search(\"://\");\n    if (i === -1) {\n      throw new Error(\"Provided address was not an absolute URL.\");\n    }\n    const origin = address.substring(i + 3); // move past the double slash\n    const protocol = address.substring(0, i);\n    let wsProtocol;\n    if (protocol === \"http\") {\n      wsProtocol = \"ws\";\n    } else if (protocol === \"https\") {\n      wsProtocol = \"wss\";\n    } else {\n      throw new Error(`Unknown parent protocol ${protocol}`);\n    }\n    const wsUri = `${wsProtocol}://${origin}/api/${version}/sync`;\n\n    this.state = new LocalSyncState();\n    this.remoteQuerySet = new RemoteQuerySet(\n      (queryId) => this.state.queryPath(queryId),\n      this.logger,\n    );\n    this.requestManager = new RequestManager(\n      this.logger,\n      this.markConnectionStateDirty,\n    );\n\n    // This is a callback for AuthenticationManager (which can't call\n    // this synchronously, the callback wouldn't work) so the initial\n    // pause for expectAuth we call it at the end of this constructor.\n    const pauseSocket = () => {\n      this.webSocketManager.pause();\n      this.state.pause();\n    };\n    this.authenticationManager = new AuthenticationManager(\n      this.state,\n      {\n        authenticate: (token) => {\n          const message = this.state.setAuth(token);\n          this.webSocketManager.sendMessage(message);\n          return message.baseVersion;\n        },\n        stopSocket: () => this.webSocketManager.stop(),\n        tryRestartSocket: () => this.webSocketManager.tryRestart(),\n        pauseSocket,\n        resumeSocket: () => this.webSocketManager.resume(),\n        clearAuth: () => {\n          this.clearAuth();\n        },\n      },\n      {\n        logger: this.logger,\n        refreshTokenLeewaySeconds: authRefreshTokenLeewaySeconds,\n      },\n    );\n    this.optimisticQueryResults = new OptimisticQueryResults();\n    this.addOnTransitionHandler((transition) => {\n      onTransition(transition.queries.map((q) => q.token));\n    });\n    this._nextRequestId = 0;\n    this._sessionId = newSessionId();\n\n    const { unsavedChangesWarning } = options;\n    if (\n      typeof window === \"undefined\" ||\n      typeof window.addEventListener === \"undefined\"\n    ) {\n      if (unsavedChangesWarning === true) {\n        throw new Error(\n          \"unsavedChangesWarning requested, but window.addEventListener not found! Remove {unsavedChangesWarning: true} from Convex client options.\",\n        );\n      }\n    } else if (unsavedChangesWarning !== false) {\n      // Listen for tab close events and notify the user on unsaved changes.\n      window.addEventListener(\"beforeunload\", (e) => {\n        if (this.requestManager.hasIncompleteRequests()) {\n          // There are 3 different ways to trigger this pop up so just try all of\n          // them.\n\n          e.preventDefault();\n          // This confirmation message doesn't actually appear in most modern\n          // browsers but we tried.\n          const confirmationMessage =\n            \"Are you sure you want to leave? Your changes may not be saved.\";\n          (e || window.event).returnValue = confirmationMessage;\n          return confirmationMessage;\n        }\n      });\n    }\n\n    this.webSocketManager = new WebSocketManager(\n      wsUri,\n      {\n        onOpen: (reconnectMetadata: ReconnectMetadata) => {\n          // We have a new WebSocket!\n          this.mark(\"convexWebSocketOpen\");\n          this.webSocketManager.sendMessage({\n            ...reconnectMetadata,\n            type: \"Connect\",\n            sessionId: this._sessionId,\n            maxObservedTimestamp: this.maxObservedTimestamp,\n          });\n\n          // Throw out our remote query, reissue queries\n          // and outstanding mutations, and reauthenticate.\n          const oldRemoteQueryResults = new Set(\n            this.remoteQuerySet.remoteQueryResults().keys(),\n          );\n          this.remoteQuerySet = new RemoteQuerySet(\n            (queryId) => this.state.queryPath(queryId),\n            this.logger,\n          );\n          const [querySetModification, authModification] = this.state.restart(\n            oldRemoteQueryResults,\n          );\n          if (authModification) {\n            this.webSocketManager.sendMessage(authModification);\n          }\n          this.webSocketManager.sendMessage(querySetModification);\n          for (const message of this.requestManager.restart()) {\n            this.webSocketManager.sendMessage(message);\n          }\n        },\n        onResume: () => {\n          const [querySetModification, authModification] = this.state.resume();\n          if (authModification) {\n            this.webSocketManager.sendMessage(authModification);\n          }\n          if (querySetModification) {\n            this.webSocketManager.sendMessage(querySetModification);\n          }\n          for (const message of this.requestManager.resume()) {\n            this.webSocketManager.sendMessage(message);\n          }\n        },\n        onMessage: (serverMessage: ServerMessage) => {\n          // Metrics events grow linearly with reconnection attempts so this\n          // conditional prevents n^2 metrics reporting.\n          if (!this.firstMessageReceived) {\n            this.firstMessageReceived = true;\n            this.mark(\"convexFirstMessageReceived\");\n            this.reportMarks();\n          }\n          switch (serverMessage.type) {\n            case \"Transition\": {\n              this.observedTimestamp(serverMessage.endVersion.ts);\n              this.authenticationManager.onTransition(serverMessage);\n              this.remoteQuerySet.transition(serverMessage);\n              this.state.transition(serverMessage);\n              const completedRequests = this.requestManager.removeCompleted(\n                this.remoteQuerySet.timestamp(),\n              );\n              this.notifyOnQueryResultChanges(completedRequests);\n              break;\n            }\n            case \"MutationResponse\": {\n              if (serverMessage.success) {\n                this.observedTimestamp(serverMessage.ts);\n              }\n              const completedMutationInfo =\n                this.requestManager.onResponse(serverMessage);\n              if (completedMutationInfo !== null) {\n                this.notifyOnQueryResultChanges(\n                  new Map([\n                    [\n                      completedMutationInfo.requestId,\n                      completedMutationInfo.result,\n                    ],\n                  ]),\n                );\n              }\n              break;\n            }\n            case \"ActionResponse\": {\n              this.requestManager.onResponse(serverMessage);\n              break;\n            }\n            case \"AuthError\": {\n              this.authenticationManager.onAuthError(serverMessage);\n              break;\n            }\n            case \"FatalError\": {\n              const error = logFatalError(this.logger, serverMessage.error);\n              void this.webSocketManager.terminate();\n              throw error;\n            }\n            case \"Ping\":\n              break; // do nothing\n            default: {\n              serverMessage satisfies never;\n            }\n          }\n\n          return {\n            hasSyncedPastLastReconnect: this.hasSyncedPastLastReconnect(),\n          };\n        },\n        onServerDisconnectError: options.onServerDisconnectError,\n      },\n      webSocketConstructor,\n      this.logger,\n      this.markConnectionStateDirty,\n    );\n    this.mark(\"convexClientConstructed\");\n\n    // Begin client in a paused state waiting for an auth token.\n    if (options.expectAuth) {\n      pauseSocket();\n    }\n  }\n\n  /**\n   * Return true if there is outstanding work from prior to the time of the most recent restart.\n   * This indicates that the client has not proven itself to have gotten past the issue that\n   * potentially led to the restart. Use this to influence when to reset backoff after a failure.\n   */\n  private hasSyncedPastLastReconnect() {\n    const hasSyncedPastLastReconnect =\n      this.requestManager.hasSyncedPastLastReconnect() ||\n      this.state.hasSyncedPastLastReconnect();\n    return hasSyncedPastLastReconnect;\n  }\n\n  private observedTimestamp(observedTs: TS) {\n    if (\n      this.maxObservedTimestamp === undefined ||\n      this.maxObservedTimestamp.lessThanOrEqual(observedTs)\n    ) {\n      this.maxObservedTimestamp = observedTs;\n    }\n  }\n\n  getMaxObservedTimestamp() {\n    return this.maxObservedTimestamp;\n  }\n\n  /**\n   * Compute the current query results based on the remoteQuerySet and the\n   * current optimistic updates and call `onTransition` for all the changed\n   * queries.\n   *\n   * @param completedMutations - A set of mutation IDs whose optimistic updates\n   * are no longer needed.\n   */\n  private notifyOnQueryResultChanges(\n    completedRequests: Map<RequestId, FunctionResult>,\n  ) {\n    const remoteQueryResults: Map<QueryId, FunctionResult> =\n      this.remoteQuerySet.remoteQueryResults();\n    const queryTokenToValue: QueryResultsMap = new Map();\n    for (const [queryId, result] of remoteQueryResults) {\n      const queryToken = this.state.queryToken(queryId);\n      // It's possible that we've already unsubscribed to this query but\n      // the server hasn't learned about that yet. If so, ignore this one.\n\n      if (queryToken !== null) {\n        const query = {\n          result,\n          udfPath: this.state.queryPath(queryId)!,\n          args: this.state.queryArgs(queryId)!,\n        };\n        queryTokenToValue.set(queryToken, query);\n      }\n    }\n\n    // Query tokens that are new (because of new server results or new local optimistic updates)\n    // or differ from old values (because of changes from local optimistic updates or new results\n    // from the server).\n    const changedQueryTokens =\n      this.optimisticQueryResults.ingestQueryResultsFromServer(\n        queryTokenToValue,\n        new Set(completedRequests.keys()),\n      );\n\n    this.handleTransition({\n      queries: changedQueryTokens.map((token) => {\n        const optimisticResult =\n          this.optimisticQueryResults.rawQueryResult(token);\n        return {\n          token,\n          modification: {\n            kind: \"Updated\",\n            result: optimisticResult!.result,\n          },\n        };\n      }),\n      reflectedMutations: Array.from(completedRequests).map(\n        ([requestId, result]) => ({\n          requestId,\n          result,\n        }),\n      ),\n      timestamp: this.remoteQuerySet.timestamp(),\n    });\n  }\n\n  private handleTransition(transition: Transition) {\n    for (const fn of this._onTransitionFns.values()) {\n      fn(transition);\n    }\n  }\n\n  /**\n   * Add a handler that will be called on a transition.\n   *\n   * Any external side effects (e.g. setting React state) should be handled here.\n   *\n   * @param fn\n   *\n   * @returns\n   */\n  addOnTransitionHandler(fn: (transition: Transition) => void) {\n    const id = this._transitionHandlerCounter++;\n    this._onTransitionFns.set(id, fn);\n    return () => this._onTransitionFns.delete(id);\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   * `fetchToken` will be called automatically again if a token expires.\n   * `fetchToken` should return `null` if the token cannot be retrieved, for example\n   * when the user's rights were permanently revoked.\n   * @param fetchToken - an async function returning the JWT-encoded OpenID Connect Identity Token\n   * @param onChange - a callback that will be called when the authentication status changes\n   */\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ) {\n    void this.authenticationManager.setConfig(fetchToken, onChange);\n  }\n\n  hasAuth() {\n    return this.state.hasAuth();\n  }\n\n  /** @internal */\n  setAdminAuth(value: string, fakeUserIdentity?: UserIdentityAttributes) {\n    const message = this.state.setAdminAuth(value, fakeUserIdentity);\n    this.webSocketManager.sendMessage(message);\n  }\n\n  clearAuth() {\n    const message = this.state.clearAuth();\n    this.webSocketManager.sendMessage(message);\n  }\n\n  /**\n   * Subscribe to a query function.\n   *\n   * Whenever this query's result changes, the `onTransition` callback\n   * passed into the constructor will be called.\n   *\n   * @param name - The name of the query.\n   * @param args - An arguments object for the query. If this is omitted, the\n   * arguments will be `{}`.\n   * @param options - A {@link SubscribeOptions} options object for this query.\n\n   * @returns An object containing a {@link QueryToken} corresponding to this\n   * query and an `unsubscribe` callback.\n   */\n  subscribe(\n    name: string,\n    args?: Record<string, Value>,\n    options?: SubscribeOptions,\n  ): { queryToken: QueryToken; unsubscribe: () => void } {\n    const argsObject = parseArgs(args);\n\n    const { modification, queryToken, unsubscribe } = this.state.subscribe(\n      name,\n      argsObject,\n      options?.journal,\n      options?.componentPath,\n    );\n    if (modification !== null) {\n      this.webSocketManager.sendMessage(modification);\n    }\n    return {\n      queryToken,\n      unsubscribe: () => {\n        const modification = unsubscribe();\n        if (modification) {\n          this.webSocketManager.sendMessage(modification);\n        }\n      },\n    };\n  }\n\n  /**\n   * A query result based only on the current, local state.\n   *\n   * The only way this will return a value is if we're already subscribed to the\n   * query or its value has been set optimistically.\n   */\n  localQueryResult(\n    udfPath: string,\n    args?: Record<string, Value>,\n  ): Value | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(udfPath, argsObject);\n    return this.optimisticQueryResults.queryResult(queryToken);\n  }\n\n  /**\n   * Get query result by query token based on current, local state\n   *\n   * The only way this will return a value is if we're already subscribed to the\n   * query or its value has been set optimistically.\n   *\n   * @internal\n   */\n  localQueryResultByToken(queryToken: QueryToken): Value | undefined {\n    return this.optimisticQueryResults.queryResult(queryToken);\n  }\n\n  /**\n   * Whether local query result is available for a toke.\n   *\n   * This method does not throw if the result is an error.\n   *\n   * @internal\n   */\n  hasLocalQueryResultByToken(queryToken: QueryToken): boolean {\n    return this.optimisticQueryResults.hasQueryResult(queryToken);\n  }\n\n  /**\n   * @internal\n   */\n  localQueryLogs(\n    udfPath: string,\n    args?: Record<string, Value>,\n  ): string[] | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(udfPath, argsObject);\n    return this.optimisticQueryResults.queryLogs(queryToken);\n  }\n\n  /**\n   * Retrieve the current {@link QueryJournal} for this query function.\n   *\n   * If we have not yet received a result for this query, this will be `undefined`.\n   *\n   * @param name - The name of the query.\n   * @param args - The arguments object for this query.\n   * @returns The query's {@link QueryJournal} or `undefined`.\n   */\n  queryJournal(\n    name: string,\n    args?: Record<string, Value>,\n  ): QueryJournal | undefined {\n    const argsObject = parseArgs(args);\n    const queryToken = serializePathAndArgs(name, argsObject);\n    return this.state.queryJournal(queryToken);\n  }\n\n  /**\n   * Get the current {@link ConnectionState} between the client and the Convex\n   * backend.\n   *\n   * @returns The {@link ConnectionState} with the Convex backend.\n   */\n  connectionState(): ConnectionState {\n    const wsConnectionState = this.webSocketManager.connectionState();\n    return {\n      hasInflightRequests: this.requestManager.hasInflightRequests(),\n      isWebSocketConnected: wsConnectionState.isConnected,\n      hasEverConnected: wsConnectionState.hasEverConnected,\n      connectionCount: wsConnectionState.connectionCount,\n      connectionRetries: wsConnectionState.connectionRetries,\n      timeOfOldestInflightRequest:\n        this.requestManager.timeOfOldestInflightRequest(),\n      inflightMutations: this.requestManager.inflightMutations(),\n      inflightActions: this.requestManager.inflightActions(),\n    };\n  }\n\n  /**\n   * Call this whenever the connection state may have changed in a way that could\n   * require publishing it. Schedules a possibly update.\n   */\n  private markConnectionStateDirty = () => {\n    void Promise.resolve().then(() => {\n      const curConnectionState = this.connectionState();\n      if (\n        JSON.stringify(curConnectionState) !==\n        JSON.stringify(this._lastPublishedConnectionState)\n      ) {\n        this._lastPublishedConnectionState = curConnectionState;\n        for (const cb of this.connectionStateSubscribers.values()) {\n          // One of these callback throwing will prevent other callbacks\n          // from running but will not leave the client in a undefined state.\n          cb(curConnectionState);\n        }\n      }\n    });\n  };\n\n  /**\n   * Subscribe to the {@link ConnectionState} between the client and the Convex\n   * backend, calling a callback each time it changes.\n   *\n   * Subscribed callbacks will be called when any part of ConnectionState changes.\n   * ConnectionState may grow in future versions (e.g. to provide a array of\n   * inflight requests) in which case callbacks would be called more frequently.\n   *\n   * @returns An unsubscribe function to stop listening.\n   */\n  subscribeToConnectionState(\n    cb: (connectionState: ConnectionState) => void,\n  ): () => void {\n    const id = this.nextConnectionStateSubscriberId++;\n    this.connectionStateSubscribers.set(id, cb);\n    return () => {\n      this.connectionStateSubscribers.delete(id);\n    };\n  }\n\n  /**\n   * Execute a mutation function.\n   *\n   * @param name - The name of the mutation.\n   * @param args - An arguments object for the mutation. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link MutationOptions} options object for this mutation.\n\n   * @returns - A promise of the mutation's result.\n   */\n  async mutation(\n    name: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n  ): Promise<any> {\n    const result = await this.mutationInternal(name, args, options);\n    if (!result.success) {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(\n            createHybridErrorStacktrace(\"mutation\", name, result),\n          ),\n        );\n      }\n      throw new Error(createHybridErrorStacktrace(\"mutation\", name, result));\n    }\n    return result.value;\n  }\n\n  /**\n   * @internal\n   */\n  async mutationInternal(\n    udfPath: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n    componentPath?: string,\n  ): Promise<FunctionResult> {\n    const { mutationPromise } = this.enqueueMutation(\n      udfPath,\n      args,\n      options,\n      componentPath,\n    );\n    return mutationPromise;\n  }\n\n  /**\n   * @internal\n   */\n  enqueueMutation(\n    udfPath: string,\n    args?: Record<string, Value>,\n    options?: MutationOptions,\n    componentPath?: string,\n  ): { requestId: RequestId; mutationPromise: Promise<FunctionResult> } {\n    const mutationArgs = parseArgs(args);\n    this.tryReportLongDisconnect();\n    const requestId = this.nextRequestId;\n    this._nextRequestId++;\n\n    if (options !== undefined) {\n      const optimisticUpdate = options.optimisticUpdate;\n      if (optimisticUpdate !== undefined) {\n        const wrappedUpdate = (localQueryStore: OptimisticLocalStore) => {\n          const result: unknown = optimisticUpdate(\n            localQueryStore,\n            mutationArgs,\n          );\n          if (result instanceof Promise) {\n            this.logger.warn(\n              \"Optimistic update handler returned a Promise. Optimistic updates should be synchronous.\",\n            );\n          }\n        };\n\n        const changedQueryTokens =\n          this.optimisticQueryResults.applyOptimisticUpdate(\n            wrappedUpdate,\n            requestId,\n          );\n\n        const changedQueries = changedQueryTokens.map((token) => {\n          const localResult = this.localQueryResultByToken(token);\n          return {\n            token,\n            modification: {\n              kind: \"Updated\" as const,\n              result:\n                localResult === undefined\n                  ? undefined\n                  : {\n                      success: true as const,\n                      value: localResult,\n                      logLines: [],\n                    },\n            },\n          };\n        });\n        this.handleTransition({\n          queries: changedQueries,\n          reflectedMutations: [],\n          timestamp: this.remoteQuerySet.timestamp(),\n        });\n      }\n    }\n\n    const message: MutationRequest = {\n      type: \"Mutation\",\n      requestId,\n      udfPath,\n      componentPath,\n      args: [convexToJson(mutationArgs)],\n    };\n    const mightBeSent = this.webSocketManager.sendMessage(message);\n    const mutationPromise = this.requestManager.request(message, mightBeSent);\n    return {\n      requestId,\n      mutationPromise,\n    };\n  }\n\n  /**\n   * Execute an action function.\n   *\n   * @param name - The name of the action.\n   * @param args - An arguments object for the action. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the action's result.\n   */\n  async action(name: string, args?: Record<string, Value>): Promise<any> {\n    const result = await this.actionInternal(name, args);\n    if (!result.success) {\n      if (result.errorData !== undefined) {\n        throw forwardData(\n          result,\n          new ConvexError(createHybridErrorStacktrace(\"action\", name, result)),\n        );\n      }\n      throw new Error(createHybridErrorStacktrace(\"action\", name, result));\n    }\n    return result.value;\n  }\n\n  /**\n   * @internal\n   */\n  async actionInternal(\n    udfPath: string,\n    args?: Record<string, Value>,\n    componentPath?: string,\n  ): Promise<FunctionResult> {\n    const actionArgs = parseArgs(args);\n    const requestId = this.nextRequestId;\n    this._nextRequestId++;\n    this.tryReportLongDisconnect();\n\n    const message: ActionRequest = {\n      type: \"Action\",\n      requestId,\n      udfPath,\n      componentPath,\n      args: [convexToJson(actionArgs)],\n    };\n\n    const mightBeSent = this.webSocketManager.sendMessage(message);\n    return this.requestManager.request(message, mightBeSent);\n  }\n\n  /**\n   * Close any network handles associated with this client and stop all subscriptions.\n   *\n   * Call this method when you're done with an {@link BaseConvexClient} to\n   * dispose of its sockets and resources.\n   *\n   * @returns A `Promise` fulfilled when the connection has been completely closed.\n   */\n  async close(): Promise<void> {\n    this.authenticationManager.stop();\n    return this.webSocketManager.terminate();\n  }\n\n  /**\n   * Return the address for this client, useful for creating a new client.\n   *\n   * Not guaranteed to match the address with which this client was constructed:\n   * it may be canonicalized.\n   */\n  get url() {\n    return this.address;\n  }\n\n  /**\n   * @internal\n   */\n  get nextRequestId() {\n    return this._nextRequestId;\n  }\n\n  /**\n   * @internal\n   */\n  get sessionId() {\n    return this._sessionId;\n  }\n\n  // Instance property so that `mark()` doesn't need to be called as a method.\n  private mark = (name: MarkName) => {\n    if (this.debug) {\n      mark(name, this.sessionId);\n    }\n  };\n\n  /**\n   * Reports performance marks to the server. This should only be called when\n   * we have a functional websocket.\n   */\n  private reportMarks() {\n    if (this.debug) {\n      const report = getMarksReport(this.sessionId);\n      this.webSocketManager.sendMessage({\n        type: \"Event\",\n        eventType: \"ClientConnect\",\n        event: report,\n      });\n    }\n  }\n\n  private tryReportLongDisconnect() {\n    if (!this.debug) {\n      return;\n    }\n    const timeOfOldestRequest =\n      this.connectionState().timeOfOldestInflightRequest;\n    if (\n      timeOfOldestRequest === null ||\n      Date.now() - timeOfOldestRequest.getTime() <= 60 * 1000\n    ) {\n      return;\n    }\n    const endpoint = `${this.address}/api/debug_event`;\n    fetch(endpoint, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Convex-Client\": `npm-${version}`,\n      },\n      body: JSON.stringify({ event: \"LongWebsocketDisconnect\" }),\n    })\n      .then((response) => {\n        if (!response.ok) {\n          this.logger.warn(\n            \"Analytics request failed with response:\",\n            response.body,\n          );\n        }\n      })\n      .catch((error) => {\n        this.logger.warn(\"Analytics response failed with error:\", error);\n      });\n  }\n}\n", "import { BaseConvexClient } from \"../browser/index.js\";\nimport type { OptimisticUpdate, QueryToken } from \"../browser/index.js\";\nimport React, { useCallback, useContext, useMemo } from \"react\";\nimport { convexToJson, Value } from \"../values/index.js\";\nimport { QueryJournal } from \"../browser/sync/protocol.js\";\nimport {\n  AuthTokenFetcher,\n  BaseConvexClientOptions,\n  ConnectionState,\n} from \"../browser/sync/client.js\";\nimport type { UserIdentityAttributes } from \"../browser/sync/protocol.js\";\nimport { RequestForQueries, useQueries } from \"./use_queries.js\";\nimport { useSubscription } from \"./use_subscription.js\";\nimport { parseArgs } from \"../common/index.js\";\nimport {\n  ArgsAndOptions,\n  FunctionArgs,\n  FunctionReference,\n  FunctionReturnType,\n  OptionalRestArgs,\n  getFunctionName,\n  makeFunctionReference,\n} from \"../server/api.js\";\nimport { EmptyObject } from \"../server/registration.js\";\nimport {\n  instantiateDefaultLogger,\n  instantiate<PERSON>oop<PERSON>ogger,\n  Lo<PERSON>,\n} from \"../browser/logging.js\";\nimport { ConvexQueryOptions } from \"../browser/query_options.js\";\n\n// When no arguments are passed, extend subscriptions (for APIs that do this by default)\n// for this amount after the subscription would otherwise be dropped.\nconst DEFAULT_EXTEND_SUBSCRIPTION_FOR = 5_000;\n\nif (typeof React === \"undefined\") {\n  throw new Error(\"Required dependency 'react' not found\");\n}\n\n// TODO Typedoc doesn't generate documentation for the comment below perhaps\n// because it's a callable interface.\n/**\n * An interface to execute a Convex mutation function on the server.\n *\n * @public\n */\nexport interface ReactMutation<Mutation extends FunctionReference<\"mutation\">> {\n  /**\n   * Execute the mutation on the server, returning a `Promise` of its return value.\n   *\n   * @param args - Arguments for the mutation to pass up to the server.\n   * @returns The return value of the server-side function call.\n   */\n  (...args: OptionalRestArgs<Mutation>): Promise<FunctionReturnType<Mutation>>;\n\n  /**\n   * Define an optimistic update to apply as part of this mutation.\n   *\n   * This is a temporary update to the local query results to facilitate a\n   * fast, interactive UI. It enables query results to update before a mutation\n   * executed on the server.\n   *\n   * When the mutation is invoked, the optimistic update will be applied.\n   *\n   * Optimistic updates can also be used to temporarily remove queries from the\n   * client and create loading experiences until a mutation completes and the\n   * new query results are synced.\n   *\n   * The update will be automatically rolled back when the mutation is fully\n   * completed and queries have been updated.\n   *\n   * @param optimisticUpdate - The optimistic update to apply.\n   * @returns A new `ReactMutation` with the update configured.\n   *\n   * @public\n   */\n  withOptimisticUpdate<T extends OptimisticUpdate<FunctionArgs<Mutation>>>(\n    optimisticUpdate: T &\n      (ReturnType<T> extends Promise<any>\n        ? \"Optimistic update handlers must be synchronous\"\n        : {}),\n  ): ReactMutation<Mutation>;\n}\n\n// Exported only for testing.\nexport function createMutation(\n  mutationReference: FunctionReference<\"mutation\">,\n  client: ConvexReactClient,\n  update?: OptimisticUpdate<any>,\n): ReactMutation<any> {\n  function mutation(args?: Record<string, Value>): Promise<unknown> {\n    assertNotAccidentalArgument(args);\n\n    return client.mutation(mutationReference, args, {\n      optimisticUpdate: update,\n    });\n  }\n  mutation.withOptimisticUpdate = function withOptimisticUpdate(\n    optimisticUpdate: OptimisticUpdate<any>,\n  ): ReactMutation<any> {\n    if (update !== undefined) {\n      throw new Error(\n        `Already specified optimistic update for mutation ${getFunctionName(\n          mutationReference,\n        )}`,\n      );\n    }\n    return createMutation(mutationReference, client, optimisticUpdate);\n  };\n  return mutation as ReactMutation<any>;\n}\n\n/**\n * An interface to execute a Convex action on the server.\n *\n * @public\n */\nexport interface ReactAction<Action extends FunctionReference<\"action\">> {\n  /**\n   * Execute the function on the server, returning a `Promise` of its return value.\n   *\n   * @param args - Arguments for the function to pass up to the server.\n   * @returns The return value of the server-side function call.\n   * @public\n   */\n  (...args: OptionalRestArgs<Action>): Promise<FunctionReturnType<Action>>;\n}\n\nfunction createAction(\n  actionReference: FunctionReference<\"action\">,\n  client: ConvexReactClient,\n): ReactAction<any> {\n  return function (args?: Record<string, Value>): Promise<unknown> {\n    return client.action(actionReference, args);\n  } as ReactAction<any>;\n}\n\n/**\n * A watch on the output of a Convex query function.\n *\n * @public\n */\nexport interface Watch<T> {\n  /**\n   * Initiate a watch on the output of a query.\n   *\n   * This will subscribe to this query and call\n   * the callback whenever the query result changes.\n   *\n   * **Important: If the client is already subscribed to this query with the\n   * same arguments this callback will not be invoked until the query result is\n   * updated.** To get the current, local result call\n   * {@link react.Watch.localQueryResult}.\n   *\n   * @param callback - Function that is called whenever the query result changes.\n   * @returns - A function that disposes of the subscription.\n   */\n  onUpdate(callback: () => void): () => void;\n\n  /**\n   * Get the current result of a query.\n   *\n   * This will only return a result if we're already subscribed to the query\n   * and have received a result from the server or the query value has been set\n   * optimistically.\n   *\n   * @returns The result of the query or `undefined` if it isn't known.\n   * @throws An error if the query encountered an error on the server.\n   */\n  localQueryResult(): T | undefined;\n\n  /**\n   * @internal\n   */\n  localQueryLogs(): string[] | undefined;\n\n  /**\n   * Get the current {@link browser.QueryJournal} for this query.\n   *\n   * If we have not yet received a result for this query, this will be `undefined`.\n   */\n  journal(): QueryJournal | undefined;\n}\n\n/**\n * Options for {@link ConvexReactClient.watchQuery}.\n *\n * @public\n */\nexport interface WatchQueryOptions {\n  /**\n   * An (optional) journal produced from a previous execution of this query\n   * function.\n   *\n   * If there is an existing subscription to a query function with the same\n   * name and arguments, this journal will have no effect.\n   */\n  journal?: QueryJournal;\n\n  /**\n   * @internal\n   */\n  componentPath?: string;\n}\n\n/**\n * Options for {@link ConvexReactClient.mutation}.\n *\n * @public\n */\nexport interface MutationOptions<Args extends Record<string, Value>> {\n  /**\n   * An optimistic update to apply along with this mutation.\n   *\n   * An optimistic update locally updates queries while a mutation is pending.\n   * Once the mutation completes, the update will be rolled back.\n   */\n  optimisticUpdate?: OptimisticUpdate<Args>;\n}\n\n/**\n * Options for {@link ConvexReactClient}.\n *\n * @public\n */\nexport interface ConvexReactClientOptions extends BaseConvexClientOptions {}\n\n/**\n * A Convex client for use within React.\n *\n * This loads reactive queries and executes mutations over a WebSocket.\n *\n * @public\n */\nexport class ConvexReactClient {\n  private address: string;\n  private cachedSync?: BaseConvexClient;\n  private listeners: Map<QueryToken, Set<() => void>>;\n  private options: ConvexReactClientOptions;\n  private closed = false;\n  private _logger: Logger;\n\n  private adminAuth?: string;\n  private fakeUserIdentity?: UserIdentityAttributes;\n\n  /**\n   * @param address - The url of your Convex deployment, often provided\n   * by an environment variable. E.g. `https://small-mouse-123.convex.cloud`.\n   * @param options - See {@link ConvexReactClientOptions} for a full description.\n   */\n  constructor(address: string, options?: ConvexReactClientOptions) {\n    // Validate address immediately since validation by the lazily-instantiated\n    // internal client does not occur synchronously.\n    if (address === undefined) {\n      throw new Error(\n        \"No address provided to ConvexReactClient.\\n\" +\n          \"If trying to deploy to production, make sure to follow all the instructions found at https://docs.convex.dev/production/hosting/\\n\" +\n          \"If running locally, make sure to run `convex dev` and ensure the .env.local file is populated.\",\n      );\n    }\n    if (typeof address !== \"string\") {\n      throw new Error(\n        `ConvexReactClient requires a URL like 'https://happy-otter-123.convex.cloud', received something of type ${typeof address} instead.`,\n      );\n    }\n    if (!address.includes(\"://\")) {\n      throw new Error(\"Provided address was not an absolute URL.\");\n    }\n    this.address = address;\n    this.listeners = new Map();\n    this._logger =\n      options?.logger === false\n        ? instantiateNoopLogger({ verbose: options?.verbose ?? false })\n        : options?.logger !== true && options?.logger\n          ? options.logger\n          : instantiateDefaultLogger({ verbose: options?.verbose ?? false });\n    this.options = { ...options, logger: this._logger };\n  }\n\n  /**\n   * Return the address for this client, useful for creating a new client.\n   *\n   * Not guaranteed to match the address with which this client was constructed:\n   * it may be canonicalized.\n   */\n  get url() {\n    return this.address;\n  }\n\n  /**\n   * Lazily instantiate the `BaseConvexClient` so we don't create the WebSocket\n   * when server-side rendering.\n   *\n   * @internal\n   */\n  get sync() {\n    if (this.closed) {\n      throw new Error(\"ConvexReactClient has already been closed.\");\n    }\n    if (this.cachedSync) {\n      return this.cachedSync;\n    }\n    this.cachedSync = new BaseConvexClient(\n      this.address,\n      (updatedQueries) => this.transition(updatedQueries),\n      this.options,\n    );\n    if (this.adminAuth) {\n      this.cachedSync.setAdminAuth(this.adminAuth, this.fakeUserIdentity);\n    }\n    return this.cachedSync;\n  }\n\n  /**\n   * Set the authentication token to be used for subsequent queries and mutations.\n   * `fetchToken` will be called automatically again if a token expires.\n   * `fetchToken` should return `null` if the token cannot be retrieved, for example\n   * when the user's rights were permanently revoked.\n   * @param fetchToken - an async function returning the JWT-encoded OpenID Connect Identity Token\n   * @param onChange - a callback that will be called when the authentication status changes\n   */\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange?: (isAuthenticated: boolean) => void,\n  ) {\n    if (typeof fetchToken === \"string\") {\n      throw new Error(\n        \"Passing a string to ConvexReactClient.setAuth is no longer supported, \" +\n          \"please upgrade to passing in an async function to handle reauthentication.\",\n      );\n    }\n    this.sync.setAuth(\n      fetchToken,\n      onChange ??\n        (() => {\n          // Do nothing\n        }),\n    );\n  }\n\n  /**\n   * Clear the current authentication token if set.\n   */\n  clearAuth() {\n    this.sync.clearAuth();\n  }\n\n  /**\n   * @internal\n   */\n  setAdminAuth(token: string, identity?: UserIdentityAttributes) {\n    this.adminAuth = token;\n    this.fakeUserIdentity = identity;\n    if (this.closed) {\n      throw new Error(\"ConvexReactClient has already been closed.\");\n    }\n    if (this.cachedSync) {\n      this.sync.setAdminAuth(token, identity);\n    }\n  }\n\n  /**\n   * Construct a new {@link Watch} on a Convex query function.\n   *\n   * **Most application code should not call this method directly. Instead use\n   * the {@link useQuery} hook.**\n   *\n   * @param query - A {@link server.FunctionReference} for the public query to run.\n   * @param args - An arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link WatchQueryOptions} options object for this query.\n   *\n   * @returns The {@link Watch} object.\n   */\n  watchQuery<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...argsAndOptions: ArgsAndOptions<Query, WatchQueryOptions>\n  ): Watch<FunctionReturnType<Query>> {\n    const [args, options] = argsAndOptions;\n    const name = getFunctionName(query);\n    return {\n      onUpdate: (callback) => {\n        const { queryToken, unsubscribe } = this.sync.subscribe(\n          name as string,\n          args,\n          options,\n        );\n\n        const currentListeners = this.listeners.get(queryToken);\n        if (currentListeners !== undefined) {\n          currentListeners.add(callback);\n        } else {\n          this.listeners.set(queryToken, new Set([callback]));\n        }\n\n        return () => {\n          if (this.closed) {\n            return;\n          }\n\n          const currentListeners = this.listeners.get(queryToken)!;\n          currentListeners.delete(callback);\n          if (currentListeners.size === 0) {\n            this.listeners.delete(queryToken);\n          }\n          unsubscribe();\n        };\n      },\n\n      localQueryResult: () => {\n        // Use the cached client because we can't have a query result if we don't\n        // even have a client yet!\n        if (this.cachedSync) {\n          return this.cachedSync.localQueryResult(name, args);\n        }\n        return undefined;\n      },\n\n      localQueryLogs: () => {\n        if (this.cachedSync) {\n          return this.cachedSync.localQueryLogs(name, args);\n        }\n        return undefined;\n      },\n\n      journal: () => {\n        if (this.cachedSync) {\n          return this.cachedSync.queryJournal(name, args);\n        }\n        return undefined;\n      },\n    };\n  }\n\n  // Let's try out a queryOptions-style API.\n  // This method is similar to the React Query API `queryClient.prefetchQuery()`.\n  // In the future an ensureQueryData(): Promise<Data> method could exist.\n  /**\n   * Indicates likely future interest in a query subscription.\n   *\n   * The implementation currently immediately subscribes to a query. In the future this method\n   * may prioritize some queries over others, fetch the query result without subscribing, or\n   * do nothing in slow network connections or high load scenarios.\n   *\n   * To use this in a React component, call useQuery() and ignore the return value.\n   *\n   * @param queryOptions - A query (function reference from an api object) and its args, plus\n   * an optional extendSubscriptionFor for how long to subscribe to the query.\n   */\n  prewarmQuery<Query extends FunctionReference<\"query\">>(\n    queryOptions: ConvexQueryOptions<Query> & {\n      extendSubscriptionFor?: number;\n    },\n  ) {\n    const extendSubscriptionFor =\n      queryOptions.extendSubscriptionFor ?? DEFAULT_EXTEND_SUBSCRIPTION_FOR;\n    const watch = this.watchQuery(queryOptions.query, queryOptions.args || {});\n    const unsubscribe = watch.onUpdate(() => {});\n    setTimeout(unsubscribe, extendSubscriptionFor);\n  }\n\n  /**\n   * Execute a mutation function.\n   *\n   * @param mutation - A {@link server.FunctionReference} for the public mutation\n   * to run.\n   * @param args - An arguments object for the mutation. If this is omitted,\n   * the arguments will be `{}`.\n   * @param options - A {@link MutationOptions} options object for the mutation.\n   * @returns A promise of the mutation's result.\n   */\n  mutation<Mutation extends FunctionReference<\"mutation\">>(\n    mutation: Mutation,\n    ...argsAndOptions: ArgsAndOptions<\n      Mutation,\n      MutationOptions<FunctionArgs<Mutation>>\n    >\n  ): Promise<FunctionReturnType<Mutation>> {\n    const [args, options] = argsAndOptions;\n    const name = getFunctionName(mutation);\n    return this.sync.mutation(name, args, options);\n  }\n\n  /**\n   * Execute an action function.\n   *\n   * @param action - A {@link server.FunctionReference} for the public action\n   * to run.\n   * @param args - An arguments object for the action. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the action's result.\n   */\n  action<Action extends FunctionReference<\"action\">>(\n    action: Action,\n    ...args: OptionalRestArgs<Action>\n  ): Promise<FunctionReturnType<Action>> {\n    const name = getFunctionName(action);\n    return this.sync.action(name, ...args);\n  }\n\n  /**\n   * Fetch a query result once.\n   *\n   * **Most application code should subscribe to queries instead, using\n   * the {@link useQuery} hook.**\n   *\n   * @param query - A {@link server.FunctionReference} for the public query\n   * to run.\n   * @param args - An arguments object for the query. If this is omitted,\n   * the arguments will be `{}`.\n   * @returns A promise of the query's result.\n   */\n  query<Query extends FunctionReference<\"query\">>(\n    query: Query,\n    ...args: OptionalRestArgs<Query>\n  ): Promise<FunctionReturnType<Query>> {\n    const watch = this.watchQuery(query, ...args);\n    const existingResult = watch.localQueryResult();\n    if (existingResult !== undefined) {\n      return Promise.resolve(existingResult);\n    }\n    return new Promise((resolve, reject) => {\n      const unsubscribe = watch.onUpdate(() => {\n        unsubscribe();\n        try {\n          resolve(watch.localQueryResult());\n        } catch (e) {\n          reject(e);\n        }\n      });\n    });\n  }\n\n  /**\n   * Get the current {@link ConnectionState} between the client and the Convex\n   * backend.\n   *\n   * @returns The {@link ConnectionState} with the Convex backend.\n   */\n  connectionState(): ConnectionState {\n    return this.sync.connectionState();\n  }\n\n  /**\n   * Subscribe to the {@link ConnectionState} between the client and the Convex\n   * backend, calling a callback each time it changes.\n   *\n   * Subscribed callbacks will be called when any part of ConnectionState changes.\n   * ConnectionState may grow in future versions (e.g. to provide a array of\n   * inflight requests) in which case callbacks would be called more frequently.\n   * ConnectionState may also *lose* properties in future versions as we figure\n   * out what information is most useful. As such this API is considered unstable.\n   *\n   * @returns An unsubscribe function to stop listening.\n   */\n  subscribeToConnectionState(\n    cb: (connectionState: ConnectionState) => void,\n  ): () => void {\n    return this.sync.subscribeToConnectionState(cb);\n  }\n\n  /**\n   * Get the logger for this client.\n   *\n   * @returns The {@link Logger} for this client.\n   */\n  get logger(): Logger {\n    return this._logger;\n  }\n\n  /**\n   * Close any network handles associated with this client and stop all subscriptions.\n   *\n   * Call this method when you're done with a {@link ConvexReactClient} to\n   * dispose of its sockets and resources.\n   *\n   * @returns A `Promise` fulfilled when the connection has been completely closed.\n   */\n  async close(): Promise<void> {\n    this.closed = true;\n    // Prevent outstanding React batched updates from invoking listeners.\n    this.listeners = new Map();\n    if (this.cachedSync) {\n      const sync = this.cachedSync;\n      this.cachedSync = undefined;\n      await sync.close();\n    }\n  }\n\n  private transition(updatedQueries: QueryToken[]) {\n    for (const queryToken of updatedQueries) {\n      const callbacks = this.listeners.get(queryToken);\n      if (callbacks) {\n        for (const callback of callbacks) {\n          callback();\n        }\n      }\n    }\n  }\n}\n\nconst ConvexContext = React.createContext<ConvexReactClient>(\n  undefined as unknown as ConvexReactClient, // in the future this will be a mocked client for testing\n);\n\n/**\n * Get the {@link ConvexReactClient} within a React component.\n *\n * This relies on the {@link ConvexProvider} being above in the React component tree.\n *\n * @returns The active {@link ConvexReactClient} object, or `undefined`.\n *\n * @public\n */\nexport function useConvex(): ConvexReactClient {\n  return useContext(ConvexContext);\n}\n\n/**\n * Provides an active Convex {@link ConvexReactClient} to descendants of this component.\n *\n * Wrap your app in this component to use Convex hooks `useQuery`,\n * `useMutation`, and `useConvex`.\n *\n * @param props - an object with a `client` property that refers to a {@link ConvexReactClient}.\n *\n * @public\n */\nexport const ConvexProvider: React.FC<{\n  client: ConvexReactClient;\n  children?: React.ReactNode;\n}> = ({ client, children }) => {\n  return React.createElement(\n    ConvexContext.Provider,\n    { value: client },\n    children,\n  );\n};\n\nexport type OptionalRestArgsOrSkip<FuncRef extends FunctionReference<any>> =\n  FuncRef[\"_args\"] extends EmptyObject\n    ? [args?: EmptyObject | \"skip\"]\n    : [args: FuncRef[\"_args\"] | \"skip\"];\n\n/**\n * Load a reactive query within a React component.\n *\n * This React hook contains internal state that will cause a rerender\n * whenever the query result changes.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param query - a {@link server.FunctionReference} for the public query to run\n * like `api.dir1.dir2.filename.func`.\n * @param args - The arguments to the query function or the string \"skip\" if the\n * query should not be loaded.\n * @returns the result of the query. If the query is loading returns `undefined`.\n *\n * @public\n */\nexport function useQuery<Query extends FunctionReference<\"query\">>(\n  query: Query,\n  ...args: OptionalRestArgsOrSkip<Query>\n): Query[\"_returnType\"] | undefined {\n  const skip = args[0] === \"skip\";\n  const argsObject = args[0] === \"skip\" ? {} : parseArgs(args[0]);\n\n  const queryReference =\n    typeof query === \"string\"\n      ? makeFunctionReference<\"query\", any, any>(query)\n      : query;\n\n  const queryName = getFunctionName(queryReference);\n\n  const queries = useMemo(\n    () =>\n      skip\n        ? ({} as RequestForQueries)\n        : { query: { query: queryReference, args: argsObject } },\n    // Stringify args so args that are semantically the same don't trigger a\n    // rerender. Saves developers from adding `useMemo` on every args usage.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [JSON.stringify(convexToJson(argsObject)), queryName, skip],\n  );\n\n  const results = useQueries(queries);\n  const result = results[\"query\"];\n  if (result instanceof Error) {\n    throw result;\n  }\n  return result;\n}\n\n/**\n * Construct a new {@link ReactMutation}.\n *\n * Mutation objects can be called like functions to request execution of the\n * corresponding Convex function, or further configured with\n * [optimistic updates](https://docs.convex.dev/using/optimistic-updates).\n *\n * The value returned by this hook is stable across renders, so it can be used\n * by React dependency arrays and memoization logic relying on object identity\n * without causing rerenders.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param mutation - A {@link server.FunctionReference} for the public mutation\n * to run like `api.dir1.dir2.filename.func`.\n * @returns The {@link ReactMutation} object with that name.\n *\n * @public\n */\nexport function useMutation<Mutation extends FunctionReference<\"mutation\">>(\n  mutation: Mutation,\n): ReactMutation<Mutation> {\n  const mutationReference =\n    typeof mutation === \"string\"\n      ? makeFunctionReference<\"mutation\", any, any>(mutation)\n      : mutation;\n\n  const convex = useContext(ConvexContext);\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useMutation` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n  return useMemo(\n    () => createMutation(mutationReference, convex),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [convex, getFunctionName(mutationReference)],\n  );\n}\n\n/**\n * Construct a new {@link ReactAction}.\n *\n * Action objects can be called like functions to request execution of the\n * corresponding Convex function.\n *\n * The value returned by this hook is stable across renders, so it can be used\n * by React dependency arrays and memoization logic relying on object identity\n * without causing rerenders.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param action - A {@link server.FunctionReference} for the public action\n * to run like `api.dir1.dir2.filename.func`.\n * @returns The {@link ReactAction} object with that name.\n *\n * @public\n */\nexport function useAction<Action extends FunctionReference<\"action\">>(\n  action: Action,\n): ReactAction<Action> {\n  const convex = useContext(ConvexContext);\n  const actionReference =\n    typeof action === \"string\"\n      ? makeFunctionReference<\"action\", any, any>(action)\n      : action;\n\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useAction` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n  return useMemo(\n    () => createAction(actionReference, convex),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [convex, getFunctionName(actionReference)],\n  );\n}\n\n/**\n * React hook to get the current {@link ConnectionState} and subscribe to changes.\n *\n * This hook returns the current connection state and automatically rerenders\n * when any part of the connection state changes (e.g., when going online/offline,\n * when requests start/complete, etc.).\n *\n * The shape of ConnectionState may change in the future which may cause this\n * hook to rerender more frequently.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @returns The current {@link ConnectionState} with the Convex backend.\n *\n * @public\n */\nexport function useConvexConnectionState(): ConnectionState {\n  const convex = useContext(ConvexContext);\n  if (convex === undefined) {\n    throw new Error(\n      \"Could not find Convex client! `useConvexConnectionState` must be used in the React component \" +\n        \"tree under `ConvexProvider`. Did you forget it? \" +\n        \"See https://docs.convex.dev/quick-start#set-up-convex-in-your-react-app\",\n    );\n  }\n\n  const getCurrentValue = useCallback(() => {\n    return convex.connectionState();\n  }, [convex]);\n\n  const subscribe = useCallback(\n    (callback: () => void) => {\n      return convex.subscribeToConnectionState(() => {\n        callback();\n      });\n    },\n    [convex],\n  );\n\n  return useSubscription({ getCurrentValue, subscribe });\n}\n\n// When a function is called with a single argument that looks like a\n// React SyntheticEvent it was likely called as an event handler.\nfunction assertNotAccidentalArgument(value: any) {\n  // these are properties of a React.SyntheticEvent\n  // https://reactjs.org/docs/events.html\n  if (\n    typeof value === \"object\" &&\n    value !== null &&\n    \"bubbles\" in value &&\n    \"persist\" in value &&\n    \"isDefaultPrevented\" in value\n  ) {\n    throw new Error(\n      `Convex function called with SyntheticEvent object. Did you use a Convex function as an event handler directly? Event handlers like onClick receive an event object as their first argument. These SyntheticEvent objects are not valid Convex values. Try wrapping the function like \\`const handler = () => myMutation();\\` and using \\`handler\\` in the event handler.`,\n    );\n  }\n}\n", "import { convexToJson, Value } from \"../values/index.js\";\nimport { Watch } from \"./client.js\";\nimport { QueryJournal } from \"../browser/sync/protocol.js\";\nimport { FunctionReference, getFunctionName } from \"../server/api.js\";\n\ntype Identifier = string;\n\ntype QueryInfo = {\n  query: FunctionReference<\"query\">;\n  args: Record<string, Value>;\n  watch: Watch<Value>;\n  unsubscribe: () => void;\n};\n\nexport type CreateWatch = (\n  query: FunctionReference<\"query\">,\n  args: Record<string, Value>,\n  journal?: QueryJournal,\n) => Watch<Value>;\n\n/**\n * A class for observing the results of multiple queries at the same time.\n *\n * Any time the result of a query changes, the listeners are notified.\n */\nexport class QueriesObserver {\n  public createWatch: CreateWatch;\n  private queries: Record<Identifier, QueryInfo>;\n  private listeners: Set<() => void>;\n\n  constructor(createWatch: CreateWatch) {\n    this.createWatch = createWatch;\n    this.queries = {};\n    this.listeners = new Set();\n  }\n\n  setQueries(\n    newQueries: Record<\n      Identifier,\n      { query: FunctionReference<\"query\">; args: Record<string, Value> }\n    >,\n  ) {\n    // Add the new queries before unsubscribing from the old ones so that\n    // the deduping in the `ConvexReactClient` can help if there are duplicates.\n    for (const identifier of Object.keys(newQueries)) {\n      const { query, args } = newQueries[identifier];\n      // Might throw\n      getFunctionName(query);\n\n      if (this.queries[identifier] === undefined) {\n        // No existing query => add it.\n        this.addQuery(identifier, query, args);\n      } else {\n        const existingInfo = this.queries[identifier];\n        if (\n          getFunctionName(query) !== getFunctionName(existingInfo.query) ||\n          JSON.stringify(convexToJson(args)) !==\n            JSON.stringify(convexToJson(existingInfo.args))\n        ) {\n          // Existing query that doesn't match => remove the old and add the new.\n          this.removeQuery(identifier);\n          this.addQuery(identifier, query, args);\n        }\n      }\n    }\n\n    // Prune all the existing queries that we no longer need.\n    for (const identifier of Object.keys(this.queries)) {\n      if (newQueries[identifier] === undefined) {\n        this.removeQuery(identifier);\n      }\n    }\n  }\n\n  subscribe(listener: () => void): () => void {\n    this.listeners.add(listener);\n    return () => {\n      this.listeners.delete(listener);\n    };\n  }\n\n  getLocalResults(\n    queries: Record<\n      Identifier,\n      { query: FunctionReference<\"query\">; args: Record<string, Value> }\n    >,\n  ): Record<Identifier, Value | undefined | Error> {\n    const result: Record<Identifier, Value | Error | undefined> = {};\n    for (const identifier of Object.keys(queries)) {\n      const { query, args } = queries[identifier];\n      // Might throw\n      getFunctionName(query);\n\n      // Note: We're not gonna watch, we could save some allocations\n      // by getting a reference to the client directly instead.\n      const watch = this.createWatch(query, args);\n      let value: Value | undefined | Error;\n      try {\n        value = watch.localQueryResult();\n      } catch (e) {\n        // Only collect instances of `Error` because thats how callers\n        // will distinguish errors from normal results.\n        if (e instanceof Error) {\n          value = e;\n        } else {\n          throw e;\n        }\n      }\n      result[identifier] = value;\n    }\n    return result;\n  }\n\n  setCreateWatch(createWatch: CreateWatch) {\n    this.createWatch = createWatch;\n    // If we have a new watch, we might be using a new Convex client.\n    // Recreate all the watches being careful to preserve the journals.\n    for (const identifier of Object.keys(this.queries)) {\n      const { query, args, watch } = this.queries[identifier];\n      const journal = watch.journal();\n      this.removeQuery(identifier);\n      this.addQuery(identifier, query, args, journal);\n    }\n  }\n\n  destroy() {\n    for (const identifier of Object.keys(this.queries)) {\n      this.removeQuery(identifier);\n    }\n    this.listeners = new Set();\n  }\n\n  private addQuery(\n    identifier: Identifier,\n    query: FunctionReference<\"query\">,\n    args: Record<string, Value>,\n    journal?: QueryJournal,\n  ) {\n    if (this.queries[identifier] !== undefined) {\n      throw new Error(\n        `Tried to add a new query with identifier ${identifier} when it already exists.`,\n      );\n    }\n    const watch = this.createWatch(query, args, journal);\n    const unsubscribe = watch.onUpdate(() => this.notifyListeners());\n    this.queries[identifier] = {\n      query,\n      args,\n      watch,\n      unsubscribe,\n    };\n  }\n\n  private removeQuery(identifier: Identifier) {\n    const info = this.queries[identifier];\n    if (info === undefined) {\n      throw new Error(`No query found with identifier ${identifier}.`);\n    }\n    info.unsubscribe();\n    delete this.queries[identifier];\n  }\n\n  private notifyListeners(): void {\n    for (const listener of this.listeners) {\n      listener();\n    }\n  }\n}\n", "import React, {\n  createContext,\n  ReactNode,\n  useContext,\n  useEffect,\n  useState,\n} from \"react\";\nimport { AuthTokenFetcher } from \"../browser/sync/client.js\";\nimport { ConvexProvider } from \"./client.js\";\n\n// Until we can import from our own entry points (requires TypeScript 4.7),\n// just describe the interface enough to help users pass the right type.\ntype IConvexReactClient = {\n  setAuth(\n    fetchToken: AuthTokenFetcher,\n    onChange: (isAuthenticated: boolean) => void,\n  ): void;\n  clearAuth(): void;\n};\n\n/**\n * Type representing the state of an auth integration with Convex.\n *\n * @public\n */\nexport type ConvexAuthState = {\n  isLoading: boolean;\n  isAuthenticated: boolean;\n};\n\nconst ConvexAuthContext = createContext<ConvexAuthState>(undefined as any);\n\n/**\n * Get the {@link ConvexAuthState} within a React component.\n *\n * This relies on a Convex auth integration provider being above in the React\n * component tree.\n *\n * @returns The current {@link ConvexAuthState}.\n *\n * @public\n */\nexport function useConvexAuth(): {\n  isLoading: boolean;\n  isAuthenticated: boolean;\n} {\n  const authContext = useContext(ConvexAuthContext);\n  if (authContext === undefined) {\n    throw new Error(\n      \"Could not find `ConvexProviderWithAuth` (or `ConvexProviderWithClerk` \" +\n        \"or `ConvexProviderWithAuth0`) \" +\n        \"as an ancestor component. This component may be missing, or you \" +\n        \"might have two instances of the `convex/react` module loaded in your \" +\n        \"project.\",\n    );\n  }\n  return authContext;\n}\n\n/**\n * A replacement for {@link ConvexProvider} which additionally provides\n * {@link ConvexAuthState} to descendants of this component.\n *\n * Use this to integrate any auth provider with Convex. The `useAuth` prop\n * should be a React hook that returns the provider's authentication state\n * and a function to fetch a JWT access token.\n *\n * If the `useAuth` prop function updates causing a rerender then auth state\n * will transition to loading and the `fetchAccessToken()` function called again.\n *\n * See [Custom Auth Integration](https://docs.convex.dev/auth/advanced/custom-auth) for more information.\n *\n * @public\n */\nexport function ConvexProviderWithAuth({\n  children,\n  client,\n  useAuth,\n}: {\n  children?: ReactNode;\n  client: IConvexReactClient;\n  useAuth: () => {\n    isLoading: boolean;\n    isAuthenticated: boolean;\n    fetchAccessToken: (args: {\n      forceRefreshToken: boolean;\n    }) => Promise<string | null>;\n  };\n}) {\n  const {\n    isLoading: authProviderLoading,\n    isAuthenticated: authProviderAuthenticated,\n    fetchAccessToken,\n  } = useAuth();\n  const [isConvexAuthenticated, setIsConvexAuthenticated] = useState<\n    boolean | null\n  >(null);\n\n  // If the useAuth went back to the authProviderLoading state (which is unusual but possible)\n  // reset the Convex auth state to null so that we can correctly\n  // transition the state from \"loading\" to \"authenticated\"\n  // without going through \"unauthenticated\".\n  if (authProviderLoading && isConvexAuthenticated !== null) {\n    setIsConvexAuthenticated(null);\n  }\n\n  // If the useAuth goes to not authenticated then isConvexAuthenticated should reflect that.\n  if (\n    !authProviderLoading &&\n    !authProviderAuthenticated &&\n    isConvexAuthenticated !== false\n  ) {\n    setIsConvexAuthenticated(false);\n  }\n\n  return (\n    <ConvexAuthContext.Provider\n      value={{\n        isLoading: isConvexAuthenticated === null,\n        isAuthenticated:\n          authProviderAuthenticated && (isConvexAuthenticated ?? false),\n      }}\n    >\n      <ConvexAuthStateFirstEffect\n        authProviderAuthenticated={authProviderAuthenticated}\n        fetchAccessToken={fetchAccessToken}\n        authProviderLoading={authProviderLoading}\n        client={client}\n        setIsConvexAuthenticated={setIsConvexAuthenticated}\n      />\n      <ConvexProvider client={client as any}>{children}</ConvexProvider>\n      <ConvexAuthStateLastEffect\n        authProviderAuthenticated={authProviderAuthenticated}\n        fetchAccessToken={fetchAccessToken}\n        authProviderLoading={authProviderLoading}\n        client={client}\n        setIsConvexAuthenticated={setIsConvexAuthenticated}\n      />\n    </ConvexAuthContext.Provider>\n  );\n}\n\n// First child ensures we `setAuth` before\n// other child components subscribe to queries via `useEffect`.\nfunction ConvexAuthStateFirstEffect({\n  authProviderAuthenticated,\n  fetchAccessToken,\n  authProviderLoading,\n  client,\n  setIsConvexAuthenticated,\n}: {\n  authProviderAuthenticated: boolean;\n  fetchAccessToken: (args: {\n    forceRefreshToken: boolean;\n  }) => Promise<string | null>;\n  authProviderLoading: boolean;\n  client: IConvexReactClient;\n  setIsConvexAuthenticated: React.Dispatch<\n    React.SetStateAction<boolean | null>\n  >;\n}) {\n  useEffect(() => {\n    let isThisEffectRelevant = true;\n    if (authProviderAuthenticated) {\n      client.setAuth(fetchAccessToken, (backendReportsIsAuthenticated) => {\n        if (isThisEffectRelevant) {\n          setIsConvexAuthenticated(() => backendReportsIsAuthenticated);\n        }\n      });\n      return () => {\n        isThisEffectRelevant = false;\n\n        // If unmounting or something changed before we finished fetching the token\n        // we shouldn't transition to a loaded state.\n        setIsConvexAuthenticated((isConvexAuthenticated) =>\n          isConvexAuthenticated ? false : null,\n        );\n      };\n    }\n  }, [\n    authProviderAuthenticated,\n    fetchAccessToken,\n    authProviderLoading,\n    client,\n    setIsConvexAuthenticated,\n  ]);\n  return null;\n}\n\n// Last child ensures we `clearAuth` last,\n// so that queries from unmounted sibling components\n// unsubscribe first and don't rerun without auth on the server\nfunction ConvexAuthStateLastEffect({\n  authProviderAuthenticated,\n  fetchAccessToken,\n  authProviderLoading,\n  client,\n  setIsConvexAuthenticated,\n}: {\n  authProviderAuthenticated: boolean;\n  fetchAccessToken: (args: {\n    forceRefreshToken: boolean;\n  }) => Promise<string | null>;\n  authProviderLoading: boolean;\n  client: IConvexReactClient;\n  setIsConvexAuthenticated: React.Dispatch<\n    React.SetStateAction<boolean | null>\n  >;\n}) {\n  useEffect(() => {\n    // If rendered with authProviderAuthenticated=true then clear that auth on in cleanup.\n    if (authProviderAuthenticated) {\n      return () => {\n        client.clearAuth();\n        // Set state back to loading in case this is a transition from one\n        // fetchToken function to another which signals a new auth context,\n        // e.g. a new orgId from Clerk. Auth context changes like this\n        // return isAuthenticated: true from useAuth() but if\n        // useAuth reports isAuthenticated: false on the next render\n        // then this null value will be overridden to false.\n        setIsConvexAuthenticated(() => null);\n      };\n    }\n  }, [\n    authProviderAuthenticated,\n    fetchAccessToken,\n    authProviderLoading,\n    client,\n    setIsConvexAuthenticated,\n  ]);\n  return null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,mBAAoC;AAwC7B,SAAS,gBAAuB;;EAErC;;;EAIA;AACF,GAGU;AAKR,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAS,OAAO;IACxC;IACA;IACA,OAAO,gBAAgB;EACzB,EAAE;AAEF,MAAI,gBAAgB,MAAM;AAG1B,MACE,MAAM,oBAAoB,mBAC1B,MAAM,cAAc,WACpB;AAIA,oBAAgB,gBAAgB;AAEhC,aAAS;MACP;MACA;MACA,OAAO;IACT,CAAC;EACH;AAWA,8BAAU,MAAM;AACd,QAAI,iBAAiB;AAErB,UAAM,kBAAkB,MAAM;AAK5B,UAAI,gBAAgB;AAClB;MACF;AAEA,eAAS,CAAC,cAAc;AAKtB,YACE,UAAU,oBAAoB,mBAC9B,UAAU,cAAc,WACxB;AACA,iBAAO;QACT;AAKA,cAAM,QAAQ,gBAAgB;AAC9B,YAAI,UAAU,UAAU,OAAO;AAC7B,iBAAO;QACT;AAEA,eAAO,EAAE,GAAG,WAAW,MAAM;MAC/B,CAAC;IACH;AACA,UAAM,cAAc,UAAU,eAAe;AAK7C,oBAAgB;AAEhB,WAAO,MAAM;AACX,uBAAiB;AACjB,kBAAY;IACd;EACF,GAAG,CAAC,iBAAiB,SAAS,CAAC;AAG/B,SAAO;AACT;;;AC1IA,IAAAA,gBAA6C;;;;;;ACK7C,IAAM,aAAa;AAInB,SAAS,kBAAkB,QAAiB;AAC1C,UAAQ,QAAQ;IACd,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;EACX;AACF;AAgBO,IAAM,gBAAN,MAAsC;EAO3C,YAAY,SAA+B;AAN3C,kBAAA,MAAQ,iBAAA;AAIR,kBAAA,MAAQ,UAAA;AAGN,SAAK,kBAAkB,CAAC;AACxB,SAAK,WAAW,QAAQ;EAC1B;EAEA,mBACE,MACY;AACZ,QAAI,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AACnD,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,UAAI,KAAK,gBAAgB,EAAE,MAAM,QAAW;AAC1C;MACF;AACA,WAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;IACjD;AACA,SAAK,gBAAgB,EAAE,IAAI;AAC3B,WAAO,MAAM;AACX,aAAO,KAAK,gBAAgB,EAAE;IAChC;EACF;EAEA,cAAc,MAAa;AACzB,QAAI,KAAK,UAAU;AACjB,iBAAW,QAAQ,OAAO,OAAO,KAAK,eAAe,GAAG;AACtD,aAAK,SAAS,IAAG,oBAAI,KAAK,GAAE,YAAY,CAAC,IAAI,GAAG,IAAI;MACtD;IACF;EACF;EAEA,OAAO,MAAa;AAClB,eAAW,QAAQ,OAAO,OAAO,KAAK,eAAe,GAAG;AACtD,WAAK,QAAQ,GAAG,IAAI;IACtB;EACF;EAEA,QAAQ,MAAa;AACnB,eAAW,QAAQ,OAAO,OAAO,KAAK,eAAe,GAAG;AACtD,WAAK,QAAQ,GAAG,IAAI;IACtB;EACF;EAEA,SAAS,MAAa;AACpB,eAAW,QAAQ,OAAO,OAAO,KAAK,eAAe,GAAG;AACtD,WAAK,SAAS,GAAG,IAAI;IACvB;EACF;AACF;AAEO,SAAS,yBAAyB,SAE9B;AACT,QAAM,SAAS,IAAI,cAAc,OAAO;AACxC,SAAO,mBAAmB,CAAC,UAAU,SAAS;AAC5C,YAAQ,OAAO;MACb,KAAK;AACH,gBAAQ,MAAM,GAAG,IAAI;AACrB;MACF,KAAK;AACH,gBAAQ,IAAI,GAAG,IAAI;AACnB;MACF,KAAK;AACH,gBAAQ,KAAK,GAAG,IAAI;AACpB;MACF,KAAK;AACH,gBAAQ,MAAM,GAAG,IAAI;AACrB;MACF,SAAS;AACP;AACA,gBAAQ,IAAI,GAAG,IAAI;MACrB;IACF;EACF,CAAC;AACD,SAAO;AACT;AAEO,SAAS,sBAAsB,SAAuC;AAC3E,SAAO,IAAI,cAAc,OAAO;AAClC;AAEO,SAAS,eACd,QACA,MACA,QACA,SACA,SACA;AACA,QAAM,SAAS,kBAAkB,MAAM;AAEvC,MAAI,OAAO,YAAY,UAAU;AAC/B,cAAU,eAAe,KAAK,UAAU,QAAQ,WAAW,MAAM,CAAC,CAAC;EACrE;AACA,MAAI,SAAS,QAAQ;AACnB,UAAM,QAAQ,QAAQ,MAAM,WAAW;AACvC,QAAI,UAAU,MAAM;AAClB,aAAO;QACL,WAAW,MAAM,IAAI,OAAO;MAC9B;AACA;IACF;AACA,UAAM,QAAQ,QAAQ,MAAM,GAAG,MAAM,CAAC,EAAE,SAAS,CAAC;AAClD,UAAM,OAAO,QAAQ,MAAM,MAAM,CAAC,EAAE,MAAM;AAE1C,WAAO,IAAI,aAAa,MAAM,IAAI,OAAO,OAAO,KAAK,KAAK,YAAY,IAAI;EAC5E,OAAO;AACL,WAAO,MAAM,WAAW,MAAM,IAAI,OAAO,MAAM,OAAO,EAAE;EAC1D;AACF;AAEO,SAAS,cAAc,QAAgB,SAAwB;AACpE,QAAM,eAAe,wBAAwB,OAAO;AACpD,SAAO,MAAM,YAAY;AACzB,SAAO,IAAI,MAAM,YAAY;AAC/B;AAEO,SAAS,4BACd,QACA,SACA,QACQ;AACR,QAAM,SAAS,kBAAkB,MAAM;AACvC,SAAO,WAAW,MAAM,IAAI,OAAO,MAAM,OAAO,YAAY;;AAC9D;AAEO,SAAS,YACd,QACA,OACA;AACC,QAA2B,OAAO,OAAO;AAC1C,SAAO;AACT;;;AC3KO,SAAS,oBAAoB,SAAyB;AAC3D,QAAM,SAAS,QAAQ,MAAM,GAAG;AAChC,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,WAAW,GAAG;AACvB,iBAAa,OAAO,CAAC;AACrB,mBAAe;EACjB,OAAO;AACL,iBAAa,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC,EAAE,KAAK,GAAG;AACxD,mBAAe,OAAO,OAAO,SAAS,CAAC;EACzC;AACA,MAAI,WAAW,SAAS,KAAK,GAAG;AAC9B,iBAAa,WAAW,MAAM,GAAG,EAAE;EACrC;AACA,SAAO,GAAG,UAAU,IAAI,YAAY;AACtC;AAWO,SAAS,qBACd,SACA,MACY;AACZ,SAAO,KAAK,UAAU;IACpB,SAAS,oBAAoB,OAAO;IACpC,MAAM,aAAa,IAAI;EACzB,CAAC;AACH;;;;;;ACPO,IAAM,iBAAN,MAAqB;EAgB1B,cAAc;AAfd,IAAAC,eAAA,MAAQ,aAAA;AACR,IAAAA,eAAA,MAAQ,iBAAA;AACR,IAAAA,eAAA,MAAiB,UAAA;AACjB,IAAAA,eAAA,MAAiB,gBAAA;AACjB,IAAAA,eAAA,MAAQ,iBAAA;AACR,IAAAA,eAAA,MAAQ,MAAA;AAKR,IAAAA,eAAA,MAAiB,oCAAA;AACjB,IAAAA,eAAA,MAAQ,iCAAA;AACR,IAAAA,eAAA,MAAQ,QAAA;AACR,IAAAA,eAAA,MAAQ,8BAAA;AAGN,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,qCAAqC,oBAAI,IAAI;AAClD,SAAK,kCAAkC;AACvC,SAAK,SAAS;AACd,SAAK,+BAA+B,oBAAI,IAAI;EAC9C;EAEA,6BAAsC;AACpC,WACE,KAAK,mCAAmC,SAAS,KACjD,CAAC,KAAK;EAEV;EAEA,qBAAqB;AACnB,SAAK,kCAAkC;EACzC;EAEA,UACE,SACA,MACA,SACA,eAKA;AACA,UAAM,uBAAuB,oBAAoB,OAAO;AACxD,UAAM,aAAa,qBAAqB,sBAAsB,IAAI;AAElE,UAAM,gBAAgB,KAAK,SAAS,IAAI,UAAU;AAElD,QAAI,kBAAkB,QAAW;AAC/B,oBAAc,kBAAkB;AAChC,aAAO;QACL;QACA,cAAc;QACd,aAAa,MAAM,KAAK,iBAAiB,UAAU;MACrD;IACF,OAAO;AACL,YAAM,UAAU,KAAK;AACrB,YAAM,QAAoB;QACxB,IAAI;QACJ;QACA;QACA,gBAAgB;QAChB;QACA;MACF;AACA,WAAK,SAAS,IAAI,YAAY,KAAK;AACnC,WAAK,eAAe,IAAI,SAAS,UAAU;AAE3C,YAAM,cAAc,KAAK;AACzB,YAAM,aAAa,KAAK,kBAAkB;AAE1C,YAAM,MAAgB;QACpB,MAAM;QACN;QACA,SAAS;QACT,MAAM,CAAC,aAAa,IAAI,CAAC;QACzB;QACA;MACF;AAEA,UAAI,KAAK,QAAQ;AACf,aAAK,6BAA6B,IAAI,SAAS,GAAG;MACpD,OAAO;AACL,aAAK,kBAAkB;MACzB;AAEA,YAAM,eAAqC;QACzC,MAAM;QACN;QACA;QACA,eAAe,CAAC,GAAG;MACrB;AACA,aAAO;QACL;QACA;QACA,aAAa,MAAM,KAAK,iBAAiB,UAAU;MACrD;IACF;EACF;EAEA,WAAW,YAAwB;AACjC,eAAW,gBAAgB,WAAW,eAAe;AACnD,cAAQ,aAAa,MAAM;QACzB,KAAK;QACL,KAAK,eAAe;AAClB,eAAK,mCAAmC,OAAO,aAAa,OAAO;AACnE,gBAAM,UAAU,aAAa;AAC7B,cAAI,YAAY,QAAW;AACzB,kBAAM,aAAa,KAAK,eAAe,IAAI,aAAa,OAAO;AAG/D,gBAAI,eAAe,QAAW;AAC5B,mBAAK,SAAS,IAAI,UAAU,EAAG,UAAU;YAC3C;UACF;AAEA;QACF;QACA,KAAK,gBAAgB;AACnB,eAAK,mCAAmC,OAAO,aAAa,OAAO;AACnE;QACF;QACA,SAAS;AAEP;AACA,gBAAM,IAAI,MAAM,wBAAyB,aAAqB,IAAI,EAAE;QACtE;MACF;IACF;EACF;EAEA,QAAQ,SAAiB,MAA6C;AACpE,UAAM,uBAAuB,oBAAoB,OAAO;AACxD,UAAM,aAAa,qBAAqB,sBAAsB,IAAI;AAClE,UAAM,gBAAgB,KAAK,SAAS,IAAI,UAAU;AAClD,QAAI,kBAAkB,QAAW;AAC/B,aAAO,cAAc;IACvB;AACA,WAAO;EACT;EAEA,4BAA4BC,UAAmC;AAC7D,WAAOA,YAAW,KAAK;EACzB;EAEA,QAAQ,OAA6B;AACnC,SAAK,OAAO;MACV,WAAW;MACX;IACF;AACA,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,kBAAkB,cAAc;IACvC;AACA,WAAO;MACL,MAAM;MACN;MACA,GAAG,KAAK;IACV;EACF;EAEA,aACE,OACA,UACqB;AACrB,UAAM,OAEF;MACF,WAAW;MACX;MACA,eAAe;IACjB;AACA,SAAK,OAAO;AACZ,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,kBAAkB,cAAc;IACvC;AACA,WAAO;MACL,MAAM;MACN;MACA,GAAG;IACL;EACF;EAEA,YAA0B;AACxB,SAAK,OAAO;AACZ,SAAK,mBAAmB;AACxB,UAAM,cAAc,KAAK;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,kBAAkB,cAAc;IACvC;AACA,WAAO;MACL,MAAM;MACN,WAAW;MACX;IACF;EACF;EAEA,UAAmB;AACjB,WAAO,CAAC,CAAC,KAAK;EAChB;EAEA,UAAU,OAAwB;;AAChC,aAAO,UAAK,SAAL,mBAAW,WAAU;EAC9B;EAEA,UAAU,SAAiC;AACzC,UAAM,cAAc,KAAK,eAAe,IAAI,OAAO;AACnD,QAAI,aAAa;AACf,aAAO,KAAK,SAAS,IAAI,WAAW,EAAG;IACzC;AACA,WAAO;EACT;EAEA,UAAU,SAAgD;AACxD,UAAM,cAAc,KAAK,eAAe,IAAI,OAAO;AACnD,QAAI,aAAa;AACf,aAAO,KAAK,SAAS,IAAI,WAAW,EAAG;IACzC;AACA,WAAO;EACT;EAEA,WAAW,SAAiC;AAC1C,WAAO,KAAK,eAAe,IAAI,OAAO,KAAK;EAC7C;EAEA,aAAa,YAAkD;;AAC7D,YAAO,UAAK,SAAS,IAAI,UAAU,MAA5B,mBAA+B;EACxC;EAEA,QACE,uBACuC;AAKvC,SAAK,QAAQ;AAEb,SAAK,mCAAmC,MAAM;AAC9C,UAAM,gBAAgB,CAAC;AACvB,eAAW,cAAc,KAAK,SAAS,OAAO,GAAG;AAC/C,YAAM,MAAgB;QACpB,MAAM;QACN,SAAS,WAAW;QACpB,SAAS,WAAW;QACpB,MAAM,CAAC,aAAa,WAAW,IAAI,CAAC;QACpC,SAAS,WAAW;QACpB,eAAe,WAAW;MAC5B;AACA,oBAAc,KAAK,GAAG;AAEtB,UAAI,CAAC,sBAAsB,IAAI,WAAW,EAAE,GAAG;AAC7C,aAAK,mCAAmC,IAAI,WAAW,EAAE;MAC3D;IACF;AACA,SAAK,kBAAkB;AACvB,UAAM,WAAiC;MACrC,MAAM;MACN,aAAa;MACb,YAAY;MACZ;IACF;AAEA,QAAI,CAAC,KAAK,MAAM;AACd,WAAK,kBAAkB;AACvB,aAAO,CAAC,UAAU,MAAS;IAC7B;AACA,SAAK,kCAAkC;AACvC,UAAM,eAA6B;MACjC,MAAM;MACN,aAAa;MACb,GAAG,KAAK;IACV;AACA,SAAK,kBAAkB;AACvB,WAAO,CAAC,UAAU,YAAY;EAChC;EAEA,QAAQ;AACN,SAAK,SAAS;EAChB;EAEA,SAAiD;AAC/C,UAAM,WACJ,KAAK,6BAA6B,OAAO,IACrC;MACE,MAAM;MACN,aAAa,KAAK;MAClB,YAAY,EAAE,KAAK;MACnB,eAAe,MAAM;QACnB,KAAK,6BAA6B,OAAO;MAC3C;IACF,IACA;AACN,UAAM,eACJ,KAAK,SAAS,SACV;MACE,MAAM;MACN,aAAa,KAAK;MAClB,GAAG,KAAK;IACV,IACA;AAEN,SAAK,QAAQ;AAEb,WAAO,CAAC,UAAU,YAAY;EAChC;EAEQ,UAAU;AAChB,SAAK,SAAS;AACd,SAAK,6BAA6B,MAAM;EAC1C;EAEQ,iBACN,YAC6B;AAC7B,UAAM,aAAa,KAAK,SAAS,IAAI,UAAU;AAE/C,QAAI,WAAW,iBAAiB,GAAG;AACjC,iBAAW,kBAAkB;AAC7B,aAAO;IACT,OAAO;AACL,WAAK,SAAS,OAAO,UAAU;AAC/B,WAAK,eAAe,OAAO,WAAW,EAAE;AACxC,WAAK,mCAAmC,OAAO,WAAW,EAAE;AAC5D,YAAM,cAAc,KAAK;AACzB,YAAM,aAAa,KAAK,kBAAkB;AAC1C,YAAM,SAAsB;QAC1B,MAAM;QACN,SAAS,WAAW;MACtB;AACA,UAAI,KAAK,QAAQ;AACf,YAAI,KAAK,6BAA6B,IAAI,WAAW,EAAE,GAAG;AACxD,eAAK,6BAA6B,OAAO,WAAW,EAAE;QACxD,OAAO;AACL,eAAK,6BAA6B,IAAI,WAAW,IAAI,MAAM;QAC7D;MACF,OAAO;AACL,aAAK,kBAAkB;MACzB;AACA,aAAO;QACL,MAAM;QACN;QACA;QACA,eAAe,CAAC,MAAM;MACxB;IACF;EACF;AACF;;;;;;ACrWO,IAAM,iBAAN,MAAqB;EAW1B,YACmB,QACA,0BACjB;AAFiB,SAAA,SAAA;AACA,SAAA,2BAAA;AAZnB,IAAAC,eAAA,MAAQ,kBAAA;AAOR,IAAAA,eAAA,MAAQ,0BAAA;AACR,IAAAA,eAAA,MAAQ,0BAAiC,CAAA;AACzC,IAAAA,eAAA,MAAQ,wBAA+B,CAAA;AAKrC,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,2BAA2B,oBAAI,IAAI;EAC1C;EAEA,QACE,SACA,MACyB;AACzB,UAAM,SAAS,IAAI,QAAwB,CAAC,YAAY;AACtD,YAAM,SAAS,OAAO,cAAc;AACpC,WAAK,iBAAiB,IAAI,QAAQ,WAAW;QAC3C;QACA,QAAQ,EAAE,QAAQ,aAAa,oBAAI,KAAK,GAAG,UAAU,QAAQ;MAC/D,CAAC;AAED,UAAI,QAAQ,SAAS,YAAY;AAC/B,aAAK;MACP,WAAW,QAAQ,SAAS,UAAU;AACpC,aAAK;MACP;IACF,CAAC;AAED,SAAK,yBAAyB;AAC9B,WAAO;EACT;;;;;;;EAQA,WACE,UACyD;AACzD,UAAM,cAAc,KAAK,iBAAiB,IAAI,SAAS,SAAS;AAChE,QAAI,gBAAgB,QAAW;AAgB7B,aAAO;IACT;AAMA,QAAI,YAAY,OAAO,WAAW,aAAa;AAC7C,aAAO;IACT;AAEA,UAAM,UACJ,YAAY,QAAQ,SAAS,aAAa,aAAa;AACzD,UAAM,UAAU,YAAY,QAAQ;AAEpC,eAAW,QAAQ,SAAS,UAAU;AACpC,qBAAe,KAAK,QAAQ,QAAQ,SAAS,SAAS,IAAI;IAC5D;AAEA,UAAM,SAAS,YAAY;AAC3B,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,SAAS;AACpB,eAAS;QACP,SAAS;QACT,UAAU,SAAS;QACnB,OAAO,aAAa,SAAS,MAAM;MACrC;AACA,kBAAY,MAAM,OAAO,SAAS,MAAM;IAC1C,OAAO;AACL,YAAM,eAAe,SAAS;AAC9B,YAAM,EAAE,UAAU,IAAI;AACtB,qBAAe,KAAK,QAAQ,SAAS,SAAS,SAAS,YAAY;AACnE,eAAS;QACP,SAAS;QACT;QACA,WACE,cAAc,SAAY,aAAa,SAAS,IAAI;QACtD,UAAU,SAAS;MACrB;AACA,kBAAY,MAAM,OAAO,SAAS,MAAM;IAC1C;AAMA,QAAI,SAAS,SAAS,oBAAoB,CAAC,SAAS,SAAS;AAC3D,gBAAU;AACV,WAAK,iBAAiB,OAAO,SAAS,SAAS;AAC/C,WAAK,yBAAyB,OAAO,SAAS,SAAS;AAEvD,UAAI,YAAY,QAAQ,SAAS,UAAU;AACzC,aAAK;MACP,WAAW,YAAY,QAAQ,SAAS,YAAY;AAClD,aAAK;MACP;AAEA,WAAK,yBAAyB;AAC9B,aAAO,EAAE,WAAW,SAAS,WAAW,OAAO;IACjD;AAIA,gBAAY,SAAS;MACnB,QAAQ;MACR;MACA,IAAI,SAAS;MACb;IACF;AAEA,WAAO;EACT;;EAGA,gBAAgB,IAA0C;AACxD,UAAM,mBAAmD,oBAAI,IAAI;AACjE,eAAW,CAAC,WAAW,WAAW,KAAK,KAAK,iBAAiB,QAAQ,GAAG;AACtE,YAAM,SAAS,YAAY;AAC3B,UAAI,OAAO,WAAW,eAAe,OAAO,GAAG,gBAAgB,EAAE,GAAG;AAClE,eAAO,UAAU;AACjB,yBAAiB,IAAI,WAAW,OAAO,MAAM;AAE7C,YAAI,YAAY,QAAQ,SAAS,YAAY;AAC3C,eAAK;QACP,WAAW,YAAY,QAAQ,SAAS,UAAU;AAChD,eAAK;QACP;AAEA,aAAK,iBAAiB,OAAO,SAAS;AACtC,aAAK,yBAAyB,OAAO,SAAS;MAChD;IACF;AACA,QAAI,iBAAiB,OAAO,GAAG;AAC7B,WAAK,yBAAyB;IAChC;AACA,WAAO;EACT;EAEA,UAA2B;AAIzB,SAAK,2BAA2B,IAAI,IAAI,KAAK,iBAAiB,KAAK,CAAC;AACpE,UAAM,cAAc,CAAC;AACrB,eAAW,CAAC,WAAW,KAAK,KAAK,KAAK,kBAAkB;AACtD,UAAI,MAAM,OAAO,WAAW,WAAW;AACrC,cAAM,OAAO,SAAS;AACtB,oBAAY,KAAK,MAAM,OAAO;AAC9B;MACF;AAEA,UAAI,MAAM,QAAQ,SAAS,YAAY;AAIrC,oBAAY,KAAK,MAAM,OAAO;MAChC,WAAW,MAAM,QAAQ,SAAS,UAAU;AAI1C,aAAK,iBAAiB,OAAO,SAAS;AACtC,aAAK,yBAAyB,OAAO,SAAS;AAC9C,aAAK;AACL,YAAI,MAAM,OAAO,WAAW,aAAa;AACvC,gBAAM,IAAI,MAAM,6CAA6C;QAC/D;AACA,cAAM,OAAO,SAAS;UACpB,SAAS;UACT,cAAc;UACd,UAAU,CAAC;QACb,CAAC;MACH;IACF;AACA,SAAK,yBAAyB;AAC9B,WAAO;EACT;EAEA,SAA0B;AACxB,UAAM,cAAc,CAAC;AACrB,eAAW,CAAC,EAAE,KAAK,KAAK,KAAK,kBAAkB;AAC7C,UAAI,MAAM,OAAO,WAAW,WAAW;AACrC,cAAM,OAAO,SAAS;AACtB,oBAAY,KAAK,MAAM,OAAO;AAC9B;MACF;IACF;AACA,WAAO;EACT;;;;;EAMA,wBAAiC;AAC/B,eAAW,eAAe,KAAK,iBAAiB,OAAO,GAAG;AACxD,UAAI,YAAY,OAAO,WAAW,aAAa;AAC7C,eAAO;MACT;IACF;AACA,WAAO;EACT;;;;;EAMA,sBAA+B;AAC7B,WAAO,KAAK,iBAAiB,OAAO;EACtC;;;;;EAMA,6BAAsC;AACpC,WAAO,KAAK,yBAAyB,SAAS;EAChD;EAEA,8BAA2C;AACzC,QAAI,KAAK,iBAAiB,SAAS,GAAG;AACpC,aAAO;IACT;AACA,QAAI,wBAAwB,KAAK,IAAI;AACrC,eAAW,WAAW,KAAK,iBAAiB,OAAO,GAAG;AACpD,UAAI,QAAQ,OAAO,WAAW,aAAa;AACzC,YAAI,QAAQ,OAAO,YAAY,QAAQ,IAAI,uBAAuB;AAChE,kCAAwB,QAAQ,OAAO,YAAY,QAAQ;QAC7D;MACF;IACF;AACA,WAAO,IAAI,KAAK,qBAAqB;EACvC;;;;EAKA,oBAA4B;AAC1B,WAAO,KAAK;EACd;;;;EAKA,kBAA0B;AACxB,WAAO,KAAK;EACd;AACF;;;;;;AC9QA,IAAM,2BAAN,MAAM,0BAAyD;EAO7D,YAAY,cAA+B;AAL3C,IAAAC,eAAA,MAAiB,cAAA;AAGjB,IAAAA,eAAA,MAAS,iBAAA;AAGP,SAAK,eAAe;AACpB,SAAK,kBAAkB,CAAC;EAC1B;EAEA,SACE,UACG,MACoC;AACvC,UAAM,YAAY,UAAU,KAAK,CAAC,CAAC;AACnC,UAAM,OAAO,gBAAgB,KAAK;AAClC,UAAM,cAAc,KAAK,aAAa;MACpC,qBAAqB,MAAM,SAAS;IACtC;AACA,QAAI,gBAAgB,QAAW;AAC7B,aAAO;IACT;AACA,WAAO,0BAAyB,WAAW,YAAY,MAAM;EAC/D;EAEA,cACE,OAIE;AACF,UAAM,kBAGA,CAAC;AACP,UAAM,OAAO,gBAAgB,KAAK;AAClC,eAAW,eAAe,KAAK,aAAa,OAAO,GAAG;AACpD,UAAI,YAAY,YAAY,oBAAoB,IAAI,GAAG;AACrD,wBAAgB,KAAK;UACnB,MAAM,YAAY;UAClB,OAAO,0BAAyB,WAAW,YAAY,MAAM;QAC/D,CAAC;MACH;IACF;AACA,WAAO;EACT;EAEA,SACE,gBACA,MACA,OACM;AACN,UAAM,YAAY,UAAU,IAAI;AAChC,UAAM,OAAO,gBAAgB,cAAc;AAC3C,UAAM,aAAa,qBAAqB,MAAM,SAAS;AAEvD,QAAI;AACJ,QAAI,UAAU,QAAW;AACvB,eAAS;IACX,OAAO;AACL,eAAS;QACP,SAAS;QACT;;QAEA,UAAU,CAAC;MACb;IACF;AACA,UAAM,QAAe;MACnB,SAAS;MACT,MAAM;MACN;IACF;AACA,SAAK,aAAa,IAAI,YAAY,KAAK;AACvC,SAAK,gBAAgB,KAAK,UAAU;EACtC;EAEA,OAAe,WACb,QACmB;AACnB,QAAI,WAAW,QAAW;AACxB,aAAO;IACT,WAAW,OAAO,SAAS;AACzB,aAAO,OAAO;IAChB,OAAO;AAKL,aAAO;IACT;EACF;AACF;AAuBO,IAAM,yBAAN,MAA6B;EAIlC,cAAc;AAHd,IAAAA,eAAA,MAAQ,cAAA;AACR,IAAAA,eAAA,MAAQ,mBAAA;AAGN,SAAK,eAAe,oBAAI,IAAI;AAC5B,SAAK,oBAAoB,CAAC;EAC5B;;;;EAKA,6BACE,oBACA,yBACgB;AAChB,SAAK,oBAAoB,KAAK,kBAAkB,OAAO,CAAC,gBAAgB;AACtE,aAAO,CAAC,wBAAwB,IAAI,YAAY,UAAU;IAC5D,CAAC;AAED,UAAM,kBAAkB,KAAK;AAC7B,SAAK,eAAe,IAAI,IAAI,kBAAkB;AAC9C,UAAM,aAAa,IAAI,yBAAyB,KAAK,YAAY;AACjE,eAAW,eAAe,KAAK,mBAAmB;AAChD,kBAAY,OAAO,UAAU;IAC/B;AAIA,UAAM,iBAAiC,CAAC;AACxC,eAAW,CAAC,YAAY,KAAK,KAAK,KAAK,cAAc;AACnD,YAAM,WAAW,gBAAgB,IAAI,UAAU;AAC/C,UAAI,aAAa,UAAa,SAAS,WAAW,MAAM,QAAQ;AAC9D,uBAAe,KAAK,UAAU;MAChC;IACF;AAEA,WAAO;EACT;EAEA,sBACE,QACA,YACgB;AAEhB,SAAK,kBAAkB,KAAK;MAC1B;MACA;IACF,CAAC;AACD,UAAM,aAAa,IAAI,yBAAyB,KAAK,YAAY;AACjE,WAAO,UAAU;AAIjB,WAAO,WAAW;EACpB;;;;EAKA,eAAe,YAA2C;AACxD,WAAO,KAAK,aAAa,IAAI,UAAU;EACzC;EAEA,YAAY,YAA2C;AACrD,UAAM,QAAQ,KAAK,aAAa,IAAI,UAAU;AAC9C,QAAI,UAAU,QAAW;AACvB,aAAO;IACT;AACA,UAAM,SAAS,MAAM;AACrB,QAAI,WAAW,QAAW;AACxB,aAAO;IACT,WAAW,OAAO,SAAS;AACzB,aAAO,OAAO;IAChB,OAAO;AACL,UAAI,OAAO,cAAc,QAAW;AAClC,cAAM;UACJ;UACA,IAAI;YACF,4BAA4B,SAAS,MAAM,SAAS,MAAM;UAC5D;QACF;MACF;AACA,YAAM,IAAI;QACR,4BAA4B,SAAS,MAAM,SAAS,MAAM;MAC5D;IACF;EACF;EAEA,eAAe,YAAiC;AAC9C,WAAO,KAAK,aAAa,IAAI,UAAU,MAAM;EAC/C;;;;EAKA,UAAU,YAA8C;;AACtD,UAAM,QAAQ,KAAK,aAAa,IAAI,UAAU;AAC9C,YAAO,oCAAO,WAAP,mBAAe;EACxB;AACF;;;;;;ACjCO,IAAM,OAAN,MAAM,MAAK;EAShB,YAAY,KAAa,MAAc;AARvC,IAAAC,eAAA,MAAA,KAAA;AACA,IAAAA,eAAA,MAAA,MAAA;AACA,IAAAA,eAAA,MAAA,oBAAA;AAOE,SAAK,MAAM,MAAM;AACjB,SAAK,OAAO,OAAO;AACnB,SAAK,qBAAqB;EAC5B;EARA,OAAO,OAAO,KAAW;AACvB,YAAQ,OAAO,IAAI,wBAAwB;EAC7C;;EASA,OAAO,YAAY,OAAuB;AACxC,WAAO,IAAI;MACT,MAAM,CAAC,IACP,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,KAAK,KACZ,MAAM,CAAC,KAAK;MACZ,MAAM,CAAC,IACP,MAAM,CAAC,KAAK,IACZ,MAAM,CAAC,KAAK,KACZ,MAAM,CAAC,KAAK;IACd;EACF;;EAGA,YAAY;AACV,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAChB,WAAO;MACL,KAAK;MACL,OAAO,IAAI;MACX,OAAO,KAAK;MACZ,OAAO;MACP,KAAK;MACL,OAAO,IAAI;MACX,OAAO,KAAK;MACZ,OAAO;IACT;EACF;EAEA,OAAO,WAAW,OAAe;AAC/B,QAAI,MAAM,KAAK,EAAG,QAAO;AACzB,QAAI,QAAQ,EAAG,QAAO;AACtB,QAAI,SAAS,eAAgB,QAAO;AACpC,WAAO,IAAI,MAAK,QAAQ,iBAAiB,GAAI,QAAQ,iBAAkB,CAAC;EAC1E;EAEA,WAAW;AACT,YACE,OAAO,KAAK,IAAI,IAAI,OAAO,cAAc,IACzC,OAAO,KAAK,GAAG,GACf,SAAS;EACb;EAEA,OAAO,OAAa;AAClB,QAAI,CAAC,MAAK,OAAO,KAAK,EAAG,SAAQ,MAAK,UAAU,KAAK;AACrD,QAAI,KAAK,SAAS,OAAO,KAAK,MAAM,SAAS,OAAO,EAAG,QAAO;AAC9D,WAAO,KAAK,SAAS,MAAM,QAAQ,KAAK,QAAQ,MAAM;EACxD;EAEA,UAAU,OAAa;AACrB,WAAO,CAAC,KAAK,OAAO,KAAK;EAC3B;EAEA,KAAK,OAAa;AAChB,QAAI,CAAC,MAAK,OAAO,KAAK,EAAG,SAAQ,MAAK,UAAU,KAAK;AACrD,QAAI,KAAK,OAAO,KAAK,EAAG,QAAO;AAC/B,WAAO,MAAM,SAAS,IAAI,KAAK,SAAS,KACrC,MAAM,SAAS,KAAK,QAAQ,MAAM,QAAQ,IAAI,KAAK,QAAQ,IAC1D,KACA;EACN;EAEA,gBAAgB,OAAa;AAC3B,WAAO,KAAK;;MAAqB;IAAK,KAAK;EAC7C;EAEA,OAAO,UAAU,KAAU;AACzB,QAAI,OAAO,QAAQ,SAAU,QAAO,MAAK,WAAW,GAAG;AAEvD,WAAO,IAAI,MAAK,IAAI,KAAK,IAAI,IAAI;EACnC;AACF;AAEA,IAAM,QAAQ,IAAI,KAAK,GAAG,CAAC;AAC3B,IAAM,iBAAiB,KAAK;AAC5B,IAAM,iBAAiB,iBAAiB;AACxC,IAAM,iBAAiB,iBAAiB;AACxC,IAAM,qBAAqB,IAAI,KAAK,aAAa,GAAG,aAAa,CAAC;;;;;;AClS3D,IAAM,iBAAN,MAAqB;EAM1B,YAAY,WAAgD,QAAgB;AAL5E,IAAAC,eAAA,MAAQ,SAAA;AACR,IAAAA,eAAA,MAAiB,gBAAA;AACjB,IAAAA,eAAA,MAAiB,WAAA;AACjB,IAAAA,eAAA,MAAiB,QAAA;AAGf,SAAK,UAAU,EAAE,UAAU,GAAG,IAAI,KAAK,WAAW,CAAC,GAAG,UAAU,EAAE;AAClE,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,YAAY;AACjB,SAAK,SAAS;EAChB;EAEA,WAAW,YAA8B;AACvC,UAAM,QAAQ,WAAW;AACzB,QACE,KAAK,QAAQ,aAAa,MAAM,YAChC,KAAK,QAAQ,GAAG,UAAU,MAAM,EAAE,KAClC,KAAK,QAAQ,aAAa,MAAM,UAChC;AACA,YAAM,IAAI;QACR,0BAA0B,MAAM,GAAG,SAAS,CAAC,IAAI,MAAM,QAAQ;MACjE;IACF;AACA,eAAW,gBAAgB,WAAW,eAAe;AACnD,cAAQ,aAAa,MAAM;QACzB,KAAK,gBAAgB;AACnB,gBAAM,YAAY,KAAK,UAAU,aAAa,OAAO;AACrD,cAAI,WAAW;AACb,uBAAW,QAAQ,aAAa,UAAU;AACxC,6BAAe,KAAK,QAAQ,QAAQ,SAAS,WAAW,IAAI;YAC9D;UACF;AACA,gBAAM,QAAQ,aAAa,aAAa,SAAS,IAAI;AACrD,eAAK,eAAe,IAAI,aAAa,SAAS;YAC5C,SAAS;YACT;YACA,UAAU,aAAa;UACzB,CAAC;AACD;QACF;QACA,KAAK,eAAe;AAClB,gBAAM,YAAY,KAAK,UAAU,aAAa,OAAO;AACrD,cAAI,WAAW;AACb,uBAAW,QAAQ,aAAa,UAAU;AACxC,6BAAe,KAAK,QAAQ,QAAQ,SAAS,WAAW,IAAI;YAC9D;UACF;AACA,gBAAM,EAAE,UAAU,IAAI;AACtB,eAAK,eAAe,IAAI,aAAa,SAAS;YAC5C,SAAS;YACT,cAAc,aAAa;YAC3B,WACE,cAAc,SAAY,aAAa,SAAS,IAAI;YACtD,UAAU,aAAa;UACzB,CAAC;AACD;QACF;QACA,KAAK,gBAAgB;AACnB,eAAK,eAAe,OAAO,aAAa,OAAO;AAC/C;QACF;QACA,SAAS;AAEP;AACA,gBAAM,IAAI,MAAM,wBAAyB,aAAqB,IAAI,EAAE;QACtE;MACF;IACF;AACA,SAAK,UAAU,WAAW;EAC5B;EAEA,qBAAmD;AACjD,WAAO,KAAK;EACd;EAEA,YAAkB;AAChB,WAAO,KAAK,QAAQ;EACtB;AACF;;;ACtFO,SAAS,UAAU,SAA0B;AAClD,QAAM,eAAe,eAAO,YAAY,OAAO;AAC/C,SAAO,KAAK,YAAY,MAAM,KAAK,YAAY,CAAC;AAClD;AAEO,SAAS,UAAU,KAAsB;AAC9C,QAAM,eAAe,IAAI,WAAW,IAAI,UAAU,CAAC;AACnD,SAAO,eAAO,cAAc,YAAY;AAC1C;AAEO,SAAS,mBACd,SACe;AACf,UAAQ,QAAQ,MAAM;IACpB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK,QAAQ;AACX,aAAO,EAAE,GAAG,QAAQ;IACtB;IACA,KAAK,oBAAoB;AACvB,UAAI,QAAQ,SAAS;AACnB,eAAO,EAAE,GAAG,SAAS,IAAI,UAAU,QAAQ,EAAE,EAAE;MACjD,OAAO;AACL,eAAO,EAAE,GAAG,QAAQ;MACtB;IACF;IACA,KAAK,cAAc;AACjB,aAAO;QACL,GAAG;QACH,cAAc;UACZ,GAAG,QAAQ;UACX,IAAI,UAAU,QAAQ,aAAa,EAAE;QACvC;QACA,YAAY;UACV,GAAG,QAAQ;UACX,IAAI,UAAU,QAAQ,WAAW,EAAE;QACrC;MACF;IACF;IACA,SAAS;AACP;IACF;EACF;AACA,SAAO;AACT;AAEO,SAAS,oBACd,SACsB;AACtB,UAAQ,QAAQ,MAAM;IACpB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK,SAAS;AACZ,aAAO,EAAE,GAAG,QAAQ;IACtB;IACA,KAAK,WAAW;AACd,UAAI,QAAQ,yBAAyB,QAAW;AAC9C,eAAO;UACL,GAAG;UACH,sBAAsB,UAAU,QAAQ,oBAAoB;QAC9D;MACF,OAAO;AACL,eAAO,EAAE,GAAG,SAAS,sBAAsB,OAAU;MACvD;IACF;IACA,SAAS;AACP;IACF;EACF;AACA,SAAO;AACT;;;;;;AC1EA,IAAM,eAAe;AACrB,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AAMxB,IAAM,kBAAkB;AA+ExB,IAAM,yBAAyB;;EAE7B,qBAAqB,EAAE,SAAS,IAAK;;EAErC,8BAA8B,EAAE,SAAS,IAAK;EAC9C,2BAA2B,EAAE,SAAS,IAAK;EAC3C,oBAAoB,EAAE,SAAS,IAAK;EACpC,6BAA6B,EAAE,SAAS,IAAK;EAC7C,kBAAkB,EAAE,SAAS,IAAK;EAClC,oBAAoB,EAAE,SAAS,IAAK;EACpC,gBAAgB,EAAE,SAAS,IAAK;;EAEhC,0BAA0B,EAAE,SAAS,IAAK;EAC1C,0BAA0B,EAAE,SAAS,IAAK;EAC1C,2BAA2B,EAAE,SAAS,IAAK;;EAE3C,qBAAqB,EAAE,SAAS,IAAK;EACrC,qBAAqB,EAAE,SAAS,IAAK;EACrC,2BAA2B,EAAE,SAAS,IAAK;AAC7C;AAIA,SAAS,wBAAwB,GAAmC;AAClE,MAAI,MAAM,OAAW,QAAO;AAG5B,aAAW,UAAU,OAAO;IAC1B;EACF,GAA8B;AAC5B,QAAI,EAAE,WAAW,MAAM,GAAG;AACxB,aAAO;IACT;EACF;AACA,SAAO;AACT;AAMO,IAAM,mBAAN,MAAuB;EAsC5B,YACE,KACA,WAMA,sBACA,QACiB,0BACjB;AADiB,SAAA,2BAAA;AA/CnB,IAAAC,eAAA,MAAQ,QAAA;AAER,IAAAA,eAAA,MAAQ,iBAAA;AACR,IAAAA,eAAA,MAAQ,qBAA6B,KAAA;AACrC,IAAAA,eAAA,MAAQ,iBAAA;AAOR,IAAAA,eAAA,MAAiB,uBAAA;AAGjB,IAAAA,eAAA,MAAiB,YAAA;AAGjB,IAAAA,eAAA,MAAQ,SAAA;AAIR,IAAAA,eAAA,MAAiB,2BAAA;AAEjB,IAAAA,eAAA,MAAQ,uCAAA;AAIR,IAAAA,eAAA,MAAiB,KAAA;AACjB,IAAAA,eAAA,MAAiB,QAAA;AACjB,IAAAA,eAAA,MAAiB,UAAA;AACjB,IAAAA,eAAA,MAAiB,WAAA;AACjB,IAAAA,eAAA,MAAiB,sBAAA;AACjB,IAAAA,eAAA,MAAiB,QAAA;AACjB,IAAAA,eAAA,MAAiB,yBAAA;AAgBf,SAAK,uBAAuB;AAC5B,SAAK,SAAS,EAAE,OAAO,eAAe;AACtC,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAGvB,SAAK,wBAAwB;AAC7B,SAAK,aAAa;AAClB,SAAK,UAAU;AAMf,SAAK,4BAA4B;AACjC,SAAK,wCAAwC;AAE7C,SAAK,MAAM;AACX,SAAK,SAAS,UAAU;AACxB,SAAK,WAAW,UAAU;AAC1B,SAAK,YAAY,UAAU;AAC3B,SAAK,0BAA0B,UAAU;AACzC,SAAK,SAAS;AAEd,SAAK,QAAQ;EACf;EAEQ,eAAe,OAAe;AACpC,SAAK,SAAS;AACd,SAAK;MACH,yBAAyB,KAAK,OAAO,KAAK,aACxC,YAAY,KAAK,SAAS,KAAK,OAAO,SAAS,MACjD;IACF;AACA,SAAK,yBAAyB;EAChC;EAEQ,UAAU;AAChB,QAAI,KAAK,OAAO,UAAU,cAAc;AACtC;IACF;AACA,QACE,KAAK,OAAO,UAAU,kBACtB,KAAK,OAAO,UAAU,WACtB;AACA,YAAM,IAAI;QACR,sDAAsD,KAAK,OAAO;MACpE;IACF;AAEA,UAAM,KAAK,IAAI,KAAK,qBAAqB,KAAK,GAAG;AACjD,SAAK,YAAY,uBAAuB;AACxC,SAAK,eAAe;MAClB,OAAO;MACP;MACA,QAAQ;IACV,CAAC;AAMD,SAAK,6BAA6B;AAElC,OAAG,SAAS,MAAM;AAChB,WAAK,OAAO,WAAW,iBAAiB;AACxC,UAAI,KAAK,OAAO,UAAU,cAAc;AACtC,cAAM,IAAI,MAAM,mDAAmD;MACrE;AACA,WAAK,eAAe;QAClB,OAAO;QACP;QACA,QAAQ,KAAK,OAAO,WAAW,QAAQ,kBAAkB;MAC3D,CAAC;AACD,WAAK,6BAA6B;AAClC,UAAI,KAAK,OAAO,WAAW,MAAM;AAC/B,aAAK,oBAAoB;AACzB,aAAK,OAAO;UACV,iBAAiB,KAAK;UACtB,iBAAiB,KAAK;QACxB,CAAC;MACH;AAEA,UAAI,KAAK,oBAAoB,kBAAkB;AAC7C,aAAK,OAAO,IAAI,uBAAuB;MACzC;AAEA,WAAK,mBAAmB;AACxB,WAAK,kBAAkB;IACzB;AAEA,OAAG,UAAU,CAAC,UAAU;AACtB,YAAM,UAAW,MAAqB;AACtC,WAAK,OAAO,IAAI,oBAAoB,OAAO,EAAE;IAC/C;AACA,OAAG,YAAY,CAAC,YAAY;AAC1B,WAAK,6BAA6B;AAClC,YAAM,gBAAgB,mBAAmB,KAAK,MAAM,QAAQ,IAAI,CAAC;AACjE,WAAK,YAAY,iCAAiC,cAAc,IAAI,EAAE;AACtE,YAAM,WAAW,KAAK,UAAU,aAAa;AAC7C,UAAI,SAAS,4BAA4B;AAEvC,aAAK,UAAU;AACf,aAAK,yBAAyB;MAChC;IACF;AACA,OAAG,UAAU,CAAC,UAAU;AACtB,WAAK,YAAY,kBAAkB;AACnC,UAAI,KAAK,oBAAoB,MAAM;AACjC,aAAK,kBAAkB,MAAM,UAAU;MACzC;AACA,UACE,MAAM,SAAS,gBACf,MAAM,SAAS;MACf,MAAM,SAAS,mBACf,MAAM,SAAS,iBACf;AACA,YAAI,MAAM,8BAA8B,MAAM,IAAI;AAClD,YAAI,MAAM,QAAQ;AAChB,iBAAO,KAAK,MAAM,MAAM;QAC1B;AACA,aAAK,OAAO,IAAI,GAAG;AACnB,YAAI,KAAK,2BAA2B,MAAM,QAAQ;AAIhD,eAAK,wBAAwB,GAAG;QAClC;MACF;AACA,YAAM,SAAS,wBAAwB,MAAM,MAAM;AACnD,WAAK,kBAAkB,MAAM;AAC7B;IACF;EACF;;;;EAKA,cAAsB;AACpB,WAAO,KAAK,OAAO;EACrB;;;;;EAMA,YAAY,SAAwB;AAClC,UAAM,gBAAgB;MACpB,MAAM,QAAQ;MACd,GAAI,QAAQ,SAAS,kBAAkB,QAAQ,cAAc,SACzD;QACE,OAAO,MAAM,QAAQ,MAAM,MAAM,EAAE,CAAC;MACtC,IACA,CAAC;IACP;AACA,QAAI,KAAK,OAAO,UAAU,WAAW,KAAK,OAAO,WAAW,MAAM;AAChE,YAAM,iBAAiB,oBAAoB,OAAO;AAClD,YAAM,UAAU,KAAK,UAAU,cAAc;AAC7C,UAAI;AACF,aAAK,OAAO,GAAG,KAAK,OAAO;MAC7B,SAAS,OAAY;AACnB,aAAK,OAAO;UACV,sDAAsD,KAAK;QAC7D;AACA,aAAK,kBAAkB,qBAAqB;MAC9C;AAEA,WAAK;QACH,0BAA0B,QAAQ,IAAI,KAAK,KAAK;UAC9C;QACF,CAAC;MACH;AACA,aAAO;IACT;AACA,SAAK;MACH,mCAAmC,KAAK,OAAO,KAAK,aAAa,YAAY,KAAK,SAAS,KAAK,OAAO,SAAS,MAAS,MAAM,KAAK;QAClI;MACF,CAAC;IACH;AAEA,WAAO;EACT;EAEQ,+BAA+B;AACrC,QAAI,KAAK,OAAO,UAAU,cAAc;AAEtC;IACF;AACA,QAAI,KAAK,0CAA0C,MAAM;AACvD,mBAAa,KAAK,qCAAqC;AACvD,WAAK,wCAAwC;IAC/C;AACA,SAAK,wCAAwC,WAAW,MAAM;AAC5D,WAAK,kBAAkB,gBAAgB;IACzC,GAAG,KAAK,yBAAyB;EACnC;EAEQ,kBAAkB,QAA0C;AAClE,SAAK,SAAS,EAAE,OAAO,eAAe;AACtC,UAAM,UAAU,KAAK,YAAY,MAAM;AACvC,SAAK,yBAAyB;AAC9B,SAAK,OAAO,IAAI,2BAA2B,OAAO,IAAI;AACtD,eAAW,MAAM,KAAK,QAAQ,GAAG,OAAO;EAC1C;;;;;;EAOQ,kBAAkB,aAAqB;AAC7C,SAAK,YAAY,uCAAuC,WAAW,EAAE;AACrE,YAAQ,KAAK,OAAO,OAAO;MACzB,KAAK;MACL,KAAK;MACL,KAAK;AAEH;MACF,KAAK;MACL,KAAK,SAAS;AACZ,aAAK,kBAAkB;AAEvB,aAAK,KAAK,MAAM;AAChB,aAAK,kBAAkB,QAAQ;AAC/B;MACF;MACA,SAAS;AAEP,aAAK;MACP;IACF;EACF;;;;;;;;EASQ,QAAuB;AAC7B,YAAQ,KAAK,OAAO,OAAO;MACzB,KAAK;MACL,KAAK;MACL,KAAK;AAEH,eAAO,QAAQ,QAAQ;MACzB,KAAK,cAAc;AACjB,cAAM,KAAK,KAAK,OAAO;AACvB,eAAO,IAAI,QAAQ,CAAC,MAAM;AACxB,aAAG,UAAU,MAAM;AACjB,iBAAK,YAAY,yBAAyB;AAC1C,cAAE;UACJ;AACA,aAAG,SAAS,MAAM;AAChB,iBAAK,YAAY,yBAAyB;AAC1C,eAAG,MAAM;UACX;QACF,CAAC;MACH;MACA,KAAK,SAAS;AACZ,aAAK,YAAY,iBAAiB;AAClC,cAAM,KAAK,KAAK,OAAO;AACvB,cAAM,SAAwB,IAAI,QAAQ,CAAC,MAAM;AAC/C,aAAG,UAAU,MAAM;AACjB,cAAE;UACJ;QACF,CAAC;AACD,WAAG,MAAM;AACT,eAAO;MACT;MACA,SAAS;AAEP,aAAK;AACL,eAAO,QAAQ,QAAQ;MACzB;IACF;EACF;;;;;EAMA,YAA2B;AACzB,QAAI,KAAK,uCAAuC;AAC9C,mBAAa,KAAK,qCAAqC;IACzD;AACA,YAAQ,KAAK,OAAO,OAAO;MACzB,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK,SAAS;AACZ,cAAM,SAAS,KAAK,MAAM;AAC1B,aAAK,eAAe,EAAE,OAAO,aAAa,CAAC;AAC3C,eAAO;MACT;MACA,SAAS;AAEP,aAAK;AACL,cAAM,IAAI;UACR,4BAA6B,KAAK,OAAe,KAAK;QACxD;MACF;IACF;EACF;EAEA,OAAsB;AACpB,YAAQ,KAAK,OAAO,OAAO;MACzB,KAAK;AAEH,eAAO,QAAQ,QAAQ;MACzB,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK,SAAS;AACZ,cAAM,SAAS,KAAK,MAAM;AAC1B,aAAK,SAAS,EAAE,OAAO,UAAU;AACjC,eAAO;MACT;MACA,SAAS;AAEP,aAAK;AACL,eAAO,QAAQ,QAAQ;MACzB;IACF;EACF;;;;;EAMA,aAAmB;AACjB,YAAQ,KAAK,OAAO,OAAO;MACzB,KAAK;AACH;MACF,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,aAAK,OAAO,WAAW,uCAAuC;AAC9D;MACF,SAAS;AAEP,aAAK;MACP;IACF;AACA,SAAK,QAAQ;EACf;EAEA,QAAc;AACZ,YAAQ,KAAK,OAAO,OAAO;MACzB,KAAK;MACL,KAAK;MACL,KAAK;AAEH;MACF,KAAK;MACL,KAAK,SAAS;AACZ,aAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,QAAQ,MAAM;AAC9C;MACF;MACA,SAAS;AAEP,aAAK;AACL;MACF;IACF;EACF;;;;EAKA,SAAe;AACb,YAAQ,KAAK,OAAO,OAAO;MACzB,KAAK;AACH,aAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,QAAQ,KAAK;AAC7C;MACF,KAAK;AACH,YAAI,KAAK,OAAO,WAAW,iBAAiB;AAC1C,eAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,QAAQ,KAAK;AAC7C,eAAK,OAAO;YACV,iBAAiB,KAAK;YACtB,iBAAiB,KAAK;UACxB,CAAC;QACH,WAAW,KAAK,OAAO,WAAW,OAAO;AACvC,eAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,QAAQ,KAAK;AAC7C,eAAK,SAAS;QAChB;AACA;MACF,KAAK;MACL,KAAK;MACL,KAAK;AAEH;MACF,SAAS;AAEP,aAAK;MACP;IACF;AACA,SAAK,QAAQ;EACf;EAEA,kBAKE;AACA,WAAO;MACL,aAAa,KAAK,OAAO,UAAU;MACnC,kBAAkB,KAAK;MACvB,iBAAiB,KAAK;MACtB,mBAAmB,KAAK;IAC1B;EACF;EAEQ,YAAY,SAAiB;AACnC,SAAK,OAAO,WAAW,OAAO;EAChC;EAEQ,YAAY,QAAkD;AACpE,UAAM,iBACJ,WAAW,WACP,MACA,WAAW,YACT,KAAK,wBACL,uBAAuB,MAAM,EAAE;AAEvC,UAAM,cAAc,iBAAiB,KAAK,IAAI,GAAG,KAAK,OAAO;AAC7D,SAAK,WAAW;AAChB,UAAM,gBAAgB,KAAK,IAAI,aAAa,KAAK,UAAU;AAC3D,UAAM,SAAS,iBAAiB,KAAK,OAAO,IAAI;AAChD,WAAO,gBAAgB;EACzB;AACF;;;AC7mBO,SAAS,eAAe;AAC7B,SAAO,OAAO;AAChB;AAGA,SAAS,SAAS;AAChB,SAAO,uCAAuC,QAAQ,SAAS,CAAC,MAAM;AACpE,UAAM,IAAK,KAAK,OAAO,IAAI,KAAM,GAC/B,IAAI,MAAM,MAAM,IAAK,IAAI,IAAO;AAClC,WAAO,EAAE,SAAS,EAAE;EACtB,CAAC;AACH;;;ACXO,IAAM,oBAAN,cAAgC,MAAM;AAC7C;AACA,kBAAkB,UAAU,OAAO;AACnC,SAAS,iBAAiB,KAAK;AAC3B,SAAO,mBAAmB,KAAK,GAAG,EAAE,QAAQ,QAAQ,CAAC,GAAG,MAAM;AAC1D,QAAI,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AACpD,QAAI,KAAK,SAAS,GAAG;AACjB,aAAO,MAAM;AAAA,IACjB;AACA,WAAO,MAAM;AAAA,EACjB,CAAC,CAAC;AACN;AACA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACrD,UAAQ,OAAO,SAAS,GAAG;AAAA,IACvB,KAAK;AACD;AAAA,IACJ,KAAK;AACD,gBAAU;AACV;AAAA,IACJ,KAAK;AACD,gBAAU;AACV;AAAA,IACJ;AACI,YAAM,IAAI,MAAM,4CAA4C;AAAA,EACpE;AACA,MAAI;AACA,WAAO,iBAAiB,MAAM;AAAA,EAClC,SACO,KAAK;AACR,WAAO,KAAK,MAAM;AAAA,EACtB;AACJ;AACO,SAAS,UAAU,OAAO,SAAS;AACtC,MAAI,OAAO,UAAU,UAAU;AAC3B,UAAM,IAAI,kBAAkB,2CAA2C;AAAA,EAC3E;AACA,cAAY,UAAU,CAAC;AACvB,QAAM,MAAM,QAAQ,WAAW,OAAO,IAAI;AAC1C,QAAM,OAAO,MAAM,MAAM,GAAG,EAAE,GAAG;AACjC,MAAI,OAAO,SAAS,UAAU;AAC1B,UAAM,IAAI,kBAAkB,0CAA0C,MAAM,CAAC,EAAE;AAAA,EACnF;AACA,MAAI;AACJ,MAAI;AACA,cAAU,gBAAgB,IAAI;AAAA,EAClC,SACO,GAAG;AACN,UAAM,IAAI,kBAAkB,qDAAqD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG;AAAA,EAC7G;AACA,MAAI;AACA,WAAO,KAAK,MAAM,OAAO;AAAA,EAC7B,SACO,GAAG;AACN,UAAM,IAAI,kBAAkB,mDAAmD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG;AAAA,EAC3G;AACJ;;;;;;ACjDA,IAAM,wBAAwB,KAAK,KAAK,KAAK,KAAK;AAElD,IAAM,kCAAkC;AAwEjC,IAAM,wBAAN,MAA4B;EAoBjC,YACE,WACA,WAQA,QAIA;AAjCF,IAAAC,eAAA,MAAQ,aAAuB,EAAE,OAAO,SAAS,CAAA;AAGjD,IAAAA,eAAA,MAAQ,iBAAgB,CAAA;AAExB,IAAAA,eAAA,MAAiB,WAAA;AAEjB,IAAAA,eAAA,MAAiB,cAAA;AACjB,IAAAA,eAAA,MAAiB,YAAA;AACjB,IAAAA,eAAA,MAAiB,kBAAA;AACjB,IAAAA,eAAA,MAAiB,aAAA;AACjB,IAAAA,eAAA,MAAiB,cAAA;AAEjB,IAAAA,eAAA,MAAiB,WAAA;AACjB,IAAAA,eAAA,MAAiB,QAAA;AACjB,IAAAA,eAAA,MAAiB,2BAAA;AAGjB,IAAAA,eAAA,MAAQ,6BAA4B,CAAA;AAgBlC,SAAK,YAAY;AACjB,SAAK,eAAe,UAAU;AAC9B,SAAK,aAAa,UAAU;AAC5B,SAAK,mBAAmB,UAAU;AAClC,SAAK,cAAc,UAAU;AAC7B,SAAK,eAAe,UAAU;AAC9B,SAAK,YAAY,UAAU;AAC3B,SAAK,SAAS,OAAO;AACrB,SAAK,4BAA4B,OAAO;EAC1C;EAEA,MAAM,UACJ,YACA,UACA;AACA,SAAK,eAAe;AACpB,SAAK,YAAY,iCAAiC;AAClD,SAAK,YAAY;AACjB,UAAM,QAAQ,MAAM,KAAK,8BAA8B,YAAY;MACjE,mBAAmB;IACrB,CAAC;AACD,QAAI,MAAM,sBAAsB;AAC9B;IACF;AACA,QAAI,MAAM,OAAO;AACf,WAAK,aAAa;QAChB,OAAO;QACP,QAAQ,EAAE,YAAY,cAAc,SAAS;QAC7C,YAAY;MACd,CAAC;AACD,WAAK,aAAa,MAAM,KAAK;IAC/B,OAAO;AACL,WAAK,aAAa;QAChB,OAAO;QACP,QAAQ,EAAE,YAAY,cAAc,SAAS;MAC/C,CAAC;AAED,YAAM,KAAK,aAAa;IAC1B;AACA,SAAK,YAAY,oCAAoC;AACrD,SAAK,aAAa;EACpB;EAEA,aAAa,eAA2B;AACtC,QACE,CAAC,KAAK,UAAU;MACd,cAAc,WAAW;IAC3B,GACA;AAGA;IACF;AACA,QACE,cAAc,WAAW,YAAY,cAAc,aAAa,UAChE;AAEA;IACF;AAEA,QAAI,KAAK,UAAU,UAAU,6CAA6C;AACxE,WAAK,YAAY,sCAAsC;AACvD,WAAK,KAAK,aAAa;AACvB,WAAK,UAAU,OAAO,aAAa,IAAI;AACvC;IACF;AACA,QAAI,KAAK,UAAU,UAAU,4CAA4C;AACvE,WAAK,YAAY,0CAA0C;AAC3D,WAAK,qBAAqB,KAAK,UAAU,KAAK;AAC9C,WAAK,4BAA4B;AACjC,UAAI,CAAC,KAAK,UAAU,SAAS;AAC3B,aAAK,UAAU,OAAO,aAAa,IAAI;MACzC;IACF;EACF;EAEA,YAAY,eAA0B;AAGpC,QACE,cAAc,wBAAwB,UACrC,KAAK,UAAU,UAAU,8CACxB,KAAK,UAAU,UAAU,8CAC3B;AACA,WAAK,YAAY,uCAAuC;AACxD;IACF;AACA,UAAM,EAAE,YAAY,IAAI;AAKxB,QAAI,CAAC,KAAK,UAAU,4BAA4B,cAAc,CAAC,GAAG;AAChE,WAAK,YAAY,+CAA+C;AAChE;IACF;AACA,SAAK,KAAK,oBAAoB,aAAa;AAC3C;EACF;;;;;EAMA,MAAc,oBAAoB,eAA0B;AAC1D,SAAK,YAAY,iCAAiC,cAAc,KAAK,EAAE;AACvE;;MAEE,KAAK,UAAU,UAAU;;MAGxB,KAAK,UAAU,UAAU,8CACxB,KAAK,6BAA6B;MACpC;AACA,WAAK,OAAO;QACV,4BAA4B,cAAc,KAAK;MACjD;AACA,UAAI,KAAK,UAAU,QAAQ,GAAG;AAC5B,aAAK,UAAU,UAAU;MAC3B;AACA,UAAI,KAAK,UAAU,UAAU,UAAU;AACrC,aAAK,uBAAuB,KAAK,UAAU,OAAO,YAAY;MAChE;AACA;IACF;AACA,QAAI,KAAK,UAAU,UAAU,4CAA4C;AACvE,WAAK;AACL,WAAK;QACH,8BAA8B,kCAAkC,KAAK,yBAAyB;MAChG;IACF;AAEA,UAAM,KAAK,WAAW;AACtB,UAAM,QAAQ,MAAM,KAAK;MACvB,KAAK,UAAU,OAAO;MACtB;QACE,mBAAmB;MACrB;IACF;AACA,QAAI,MAAM,sBAAsB;AAC9B;IACF;AAEA,QAAI,MAAM,SAAS,KAAK,UAAU,UAAU,MAAM,KAAK,GAAG;AACxD,WAAK,aAAa,MAAM,KAAK;AAC7B,WAAK,aAAa;QAChB,OAAO;QACP,QAAQ,KAAK,UAAU;QACvB,OAAO,MAAM;QACb,SACE,KAAK,UAAU,UAAU,mBACzB,KAAK,UAAU,UAAU;MAC7B,CAAC;IACH,OAAO;AACL,WAAK,YAAY,sDAAsD;AACvE,UAAI,KAAK,UAAU,QAAQ,GAAG;AAC5B,aAAK,UAAU,UAAU;MAC3B;AACA,WAAK,uBAAuB,KAAK,UAAU,OAAO,YAAY;IAChE;AACA,SAAK,iBAAiB;EACxB;;;;EAKA,MAAc,eAAe;AAC3B,QAAI,KAAK,UAAU,UAAU,UAAU;AACrC;IACF;AACA,SAAK,YAAY,uBAAuB;AACxC,UAAM,QAAQ,MAAM,KAAK;MACvB,KAAK,UAAU,OAAO;MACtB;QACE,mBAAmB;MACrB;IACF;AACA,QAAI,MAAM,sBAAsB;AAC9B;IACF;AAEA,QAAI,MAAM,OAAO;AACf,UAAI,KAAK,UAAU,UAAU,MAAM,KAAK,GAAG;AACzC,aAAK,aAAa;UAChB,OAAO;UACP,SAAS,KAAK,UAAU,QAAQ;UAChC,OAAO,MAAM;UACb,QAAQ,KAAK,UAAU;QACzB,CAAC;AACD,aAAK,aAAa,MAAM,KAAK;MAC/B,OAAO;AACL,aAAK,aAAa;UAChB,OAAO;UACP,QAAQ,KAAK,UAAU;QACzB,CAAC;MACH;IACF,OAAO;AACL,WAAK,YAAY,yBAAyB;AAC1C,UAAI,KAAK,UAAU,QAAQ,GAAG;AAC5B,aAAK,UAAU;MACjB;AACA,WAAK,uBAAuB,KAAK,UAAU,OAAO,YAAY;IAChE;AAGA,SAAK;MACH;IACF;AACA,SAAK,iBAAiB;EACxB;EAEQ,qBAAqB,OAAe;AAC1C,QAAI,KAAK,UAAU,UAAU,UAAU;AACrC;IACF;AACA,UAAM,eAAe,KAAK,YAAY,KAAK;AAC3C,QAAI,CAAC,cAAc;AAIjB,WAAK,OAAO;QACV;MACF;AACA;IACF;AAGA,UAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAI,CAAC,OAAO,CAAC,KAAK;AAChB,WAAK,OAAO;QACV;MACF;AACA;IACF;AAKA,UAAM,uBAAuB,MAAM;AACnC,QAAI,wBAAwB,GAAG;AAC7B,WAAK,OAAO;QACV;MACF;AACA;IACF;AAGA,QAAI,QAAQ,KAAK;MACf;OACC,uBAAuB,KAAK,6BAA6B;IAC5D;AACA,QAAI,SAAS,GAAG;AAGd,WAAK,OAAO;QACV,wDAAwD,KAAK,yBAAyB,yCAAyC,oBAAoB;MACrJ;AACA,cAAQ;IACV;AACA,UAAM,wBAAwB,WAAW,MAAM;AAC7C,WAAK,YAAY,iCAAiC;AAClD,WAAK,KAAK,aAAa;IACzB,GAAG,KAAK;AACR,SAAK,aAAa;MAChB,OAAO;MACP;MACA,QAAQ,KAAK,UAAU;IACzB,CAAC;AACD,SAAK;MACH,iDAAiD,KAAK;IACxD;EACF;;;EAIA,MAAc,8BACZ,YACA,WAGA;AACA,UAAM,wBAAwB,EAAE,KAAK;AACrC,SAAK;MACH,sCAAsC,qBAAqB;IAC7D;AACA,UAAM,QAAQ,MAAM,WAAW,SAAS;AACxC,QAAI,KAAK,kBAAkB,uBAAuB;AAEhD,WAAK;QACH,kCAAkC,qBAAqB,SAAS,KAAK,aAAa;MACpF;AACA,aAAO,EAAE,sBAAsB,KAAK;IACtC;AACA,WAAO,EAAE,sBAAsB,OAAO,OAAO,MAAM;EACrD;EAEA,OAAO;AACL,SAAK,eAAe;AAEpB,SAAK;AACL,SAAK,YAAY,4BAA4B,KAAK,aAAa,EAAE;EACnE;EAEQ,uBACN,cACA;AACA,iBAAa,KAAK;AAClB,SAAK,eAAe;EACtB;EAEQ,iBAAiB;AACvB,SAAK,aAAa,EAAE,OAAO,SAAS,CAAC;EACvC;EAEQ,aAAa,SAAoB;AACvC,UAAM,kBACJ,QAAQ,UAAU,6CACd;MACE,SAAS,QAAQ;MACjB,OAAO,QAAQ;MACf,OAAO,MAAM,QAAQ,MAAM,MAAM,EAAE,CAAC;IACtC,IACA,EAAE,OAAO,QAAQ,MAAM;AAC7B,SAAK;MACH,yBAAyB,KAAK,UAAU,eAAe,CAAC;IAC1D;AACA,YAAQ,QAAQ,OAAO;MACrB,KAAK;MACL,KAAK;MACL,KAAK;AACH,aAAK,4BAA4B;AACjC;MACF,KAAK;MACL,KAAK;MACL,KAAK;AACH;MACF,SAAS;AACP;MACF;IACF;AACA,QAAI,KAAK,UAAU,UAAU,8BAA8B;AACzD,mBAAa,KAAK,UAAU,qBAAqB;AAIjD,WAAK,UAAU,mBAAmB;IACpC;AACA,SAAK,YAAY;EACnB;EAEQ,YAAY,OAAe;AACjC,QAAI;AACF,aAAO,UAAU,KAAK;IACxB,SAAS,GAAG;AACV,WAAK;QACH,yBAAyB,aAAa,QAAQ,EAAE,UAAU,eAAe;MAC3E;AACA,aAAO;IACT;EACF;EAEQ,YAAY,SAAiB;AACnC,SAAK,OAAO,WAAW,GAAG,OAAO,MAAM,KAAK,aAAa,GAAG;EAC9D;AACF;;;AC/dA,IAAM,YAAY;EAChB;EACA;EACA;AACF;AAYO,SAAS,KAAK,MAAgB,WAAmB;AACtD,QAAM,SAAqB,EAAE,UAAU;AAGvC,MAAI,OAAO,gBAAgB,eAAe,CAAC,YAAY,KAAM;AAC7D,cAAY,KAAK,MAAM,EAAE,OAAO,CAAC;AACnC;AAIA,SAAS,sBAAsBC,OAAiC;AAE9D,MAAI,OAAOA,MAAK,KAAK,MAAM,SAAS,MAAM;AAE1C,SAAO,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AAClD,SAAO;IACL;IACA,WAAWA,MAAK;EAClB;AACF;AAUO,SAAS,eAAe,WAA+B;AAC5D,MAAI,OAAO,gBAAgB,eAAe,CAAC,YAAY,kBAAkB;AACvE,WAAO,CAAC;EACV;AACA,QAAM,WAA8B,CAAC;AACrC,aAAW,QAAQ,WAAW;AAC5B,UAAM,QACJ,YACG,iBAAiB,IAAI,EACrB,OAAO,CAAC,UAAU,MAAM,cAAc,MAAM,EAC/C,OAAO,CAACA,UAASA,MAAK,OAAO,cAAc,SAAS;AACtD,aAAS,KAAK,GAAG,KAAK;EACxB;AACA,SAAO,SAAS,IAAI,qBAAqB;AAC3C;;;;;;AC8KO,IAAM,mBAAN,MAAuB;;;;;;;;;EAgC5B,YACE,SACA,cACA,SACA;AAnCF,IAAAC,eAAA,MAAiB,SAAA;AACjB,IAAAA,eAAA,MAAiB,OAAA;AACjB,IAAAA,eAAA,MAAiB,gBAAA;AACjB,IAAAA,eAAA,MAAiB,kBAAA;AACjB,IAAAA,eAAA,MAAiB,uBAAA;AACjB,IAAAA,eAAA,MAAQ,gBAAA;AACR,IAAAA,eAAA,MAAiB,wBAAA;AACjB,IAAAA,eAAA,MAAQ,6BAA4B,CAAA;AACpC,IAAAA,eAAA,MAAQ,gBAAA;AACR,IAAAA,eAAA,MAAQ,oBACN,oBAAI,IAAI,CAAA;AACV,IAAAA,eAAA,MAAiB,YAAA;AACjB,IAAAA,eAAA,MAAQ,wBAAuB,KAAA;AAC/B,IAAAA,eAAA,MAAiB,OAAA;AACjB,IAAAA,eAAA,MAAiB,QAAA;AACjB,IAAAA,eAAA,MAAQ,sBAAA;AACR,IAAAA,eAAA,MAAQ,8BAA6B,oBAAI,IAGvC,CAAA;AACF,IAAAA,eAAA,MAAQ,mCAA0C,CAAA;AAClD,IAAAA,eAAA,MAAQ,+BAAA;AA0gBR,IAAAA,eAAA,MAAQ,4BAA2B,MAAM;AACvC,WAAK,QAAQ,QAAQ,EAAE,KAAK,MAAM;AAChC,cAAM,qBAAqB,KAAK,gBAAgB;AAChD,YACE,KAAK,UAAU,kBAAkB,MACjC,KAAK,UAAU,KAAK,6BAA6B,GACjD;AACA,eAAK,gCAAgC;AACrC,qBAAW,MAAM,KAAK,2BAA2B,OAAO,GAAG;AAGzD,eAAG,kBAAkB;UACvB;QACF;MACF,CAAC;IACH,CAAA;AAsOA,IAAAA,eAAA,MAAQ,QAAO,CAAC,SAAmB;AACjC,UAAI,KAAK,OAAO;AACd,aAAK,MAAM,KAAK,SAAS;MAC3B;IACF,CAAA;AApvBE,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,IAAI;QACR;MACF;IACF;AACA,SAAI,mCAAS,kCAAiC,MAAM;AAClD,4BAAsB,OAAO;IAC/B;AACA,cAAU,EAAE,GAAG,QAAQ;AACvB,UAAM,gCACJ,QAAQ,iCAAiC;AAC3C,QAAI,uBAAuB,QAAQ;AACnC,QAAI,CAAC,wBAAwB,OAAO,cAAc,aAAa;AAC7D,YAAM,IAAI;QACR;MACF;IACF;AACA,2BAAuB,wBAAwB;AAC/C,SAAK,QAAQ,QAAQ,2BAA2B;AAChD,SAAK,UAAU;AACf,SAAK,SACH,QAAQ,WAAW,QACf,sBAAsB,EAAE,SAAS,QAAQ,WAAW,MAAM,CAAC,IAC3D,QAAQ,WAAW,QAAQ,QAAQ,SACjC,QAAQ,SACR,yBAAyB,EAAE,SAAS,QAAQ,WAAW,MAAM,CAAC;AAEtE,UAAM,IAAI,QAAQ,OAAO,KAAK;AAC9B,QAAI,MAAM,IAAI;AACZ,YAAM,IAAI,MAAM,2CAA2C;IAC7D;AACA,UAAM,SAAS,QAAQ,UAAU,IAAI,CAAC;AACtC,UAAM,WAAW,QAAQ,UAAU,GAAG,CAAC;AACvC,QAAI;AACJ,QAAI,aAAa,QAAQ;AACvB,mBAAa;IACf,WAAW,aAAa,SAAS;AAC/B,mBAAa;IACf,OAAO;AACL,YAAM,IAAI,MAAM,2BAA2B,QAAQ,EAAE;IACvD;AACA,UAAM,QAAQ,GAAG,UAAU,MAAM,MAAM,QAAQ,OAAO;AAEtD,SAAK,QAAQ,IAAI,eAAe;AAChC,SAAK,iBAAiB,IAAI;MACxB,CAAC,YAAY,KAAK,MAAM,UAAU,OAAO;MACzC,KAAK;IACP;AACA,SAAK,iBAAiB,IAAI;MACxB,KAAK;MACL,KAAK;IACP;AAKA,UAAM,cAAc,MAAM;AACxB,WAAK,iBAAiB,MAAM;AAC5B,WAAK,MAAM,MAAM;IACnB;AACA,SAAK,wBAAwB,IAAI;MAC/B,KAAK;MACL;QACE,cAAc,CAAC,UAAU;AACvB,gBAAM,UAAU,KAAK,MAAM,QAAQ,KAAK;AACxC,eAAK,iBAAiB,YAAY,OAAO;AACzC,iBAAO,QAAQ;QACjB;QACA,YAAY,MAAM,KAAK,iBAAiB,KAAK;QAC7C,kBAAkB,MAAM,KAAK,iBAAiB,WAAW;QACzD;QACA,cAAc,MAAM,KAAK,iBAAiB,OAAO;QACjD,WAAW,MAAM;AACf,eAAK,UAAU;QACjB;MACF;MACA;QACE,QAAQ,KAAK;QACb,2BAA2B;MAC7B;IACF;AACA,SAAK,yBAAyB,IAAI,uBAAuB;AACzD,SAAK,uBAAuB,CAAC,eAAe;AAC1C,mBAAa,WAAW,QAAQ,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;IACrD,CAAC;AACD,SAAK,iBAAiB;AACtB,SAAK,aAAa,aAAa;AAE/B,UAAM,EAAE,sBAAsB,IAAI;AAClC,QACE,OAAO,WAAW,eAClB,OAAO,OAAO,qBAAqB,aACnC;AACA,UAAI,0BAA0B,MAAM;AAClC,cAAM,IAAI;UACR;QACF;MACF;IACF,WAAW,0BAA0B,OAAO;AAE1C,aAAO,iBAAiB,gBAAgB,CAAC,MAAM;AAC7C,YAAI,KAAK,eAAe,sBAAsB,GAAG;AAI/C,YAAE,eAAe;AAGjB,gBAAM,sBACJ;AACF,WAAC,KAAK,OAAO,OAAO,cAAc;AAClC,iBAAO;QACT;MACF,CAAC;IACH;AAEA,SAAK,mBAAmB,IAAI;MAC1B;MACA;QACE,QAAQ,CAAC,sBAAyC;AAEhD,eAAK,KAAK,qBAAqB;AAC/B,eAAK,iBAAiB,YAAY;YAChC,GAAG;YACH,MAAM;YACN,WAAW,KAAK;YAChB,sBAAsB,KAAK;UAC7B,CAAC;AAID,gBAAM,wBAAwB,IAAI;YAChC,KAAK,eAAe,mBAAmB,EAAE,KAAK;UAChD;AACA,eAAK,iBAAiB,IAAI;YACxB,CAAC,YAAY,KAAK,MAAM,UAAU,OAAO;YACzC,KAAK;UACP;AACA,gBAAM,CAAC,sBAAsB,gBAAgB,IAAI,KAAK,MAAM;YAC1D;UACF;AACA,cAAI,kBAAkB;AACpB,iBAAK,iBAAiB,YAAY,gBAAgB;UACpD;AACA,eAAK,iBAAiB,YAAY,oBAAoB;AACtD,qBAAW,WAAW,KAAK,eAAe,QAAQ,GAAG;AACnD,iBAAK,iBAAiB,YAAY,OAAO;UAC3C;QACF;QACA,UAAU,MAAM;AACd,gBAAM,CAAC,sBAAsB,gBAAgB,IAAI,KAAK,MAAM,OAAO;AACnE,cAAI,kBAAkB;AACpB,iBAAK,iBAAiB,YAAY,gBAAgB;UACpD;AACA,cAAI,sBAAsB;AACxB,iBAAK,iBAAiB,YAAY,oBAAoB;UACxD;AACA,qBAAW,WAAW,KAAK,eAAe,OAAO,GAAG;AAClD,iBAAK,iBAAiB,YAAY,OAAO;UAC3C;QACF;QACA,WAAW,CAAC,kBAAiC;AAG3C,cAAI,CAAC,KAAK,sBAAsB;AAC9B,iBAAK,uBAAuB;AAC5B,iBAAK,KAAK,4BAA4B;AACtC,iBAAK,YAAY;UACnB;AACA,kBAAQ,cAAc,MAAM;YAC1B,KAAK,cAAc;AACjB,mBAAK,kBAAkB,cAAc,WAAW,EAAE;AAClD,mBAAK,sBAAsB,aAAa,aAAa;AACrD,mBAAK,eAAe,WAAW,aAAa;AAC5C,mBAAK,MAAM,WAAW,aAAa;AACnC,oBAAM,oBAAoB,KAAK,eAAe;gBAC5C,KAAK,eAAe,UAAU;cAChC;AACA,mBAAK,2BAA2B,iBAAiB;AACjD;YACF;YACA,KAAK,oBAAoB;AACvB,kBAAI,cAAc,SAAS;AACzB,qBAAK,kBAAkB,cAAc,EAAE;cACzC;AACA,oBAAM,wBACJ,KAAK,eAAe,WAAW,aAAa;AAC9C,kBAAI,0BAA0B,MAAM;AAClC,qBAAK;kBACH,oBAAI,IAAI;oBACN;sBACE,sBAAsB;sBACtB,sBAAsB;oBACxB;kBACF,CAAC;gBACH;cACF;AACA;YACF;YACA,KAAK,kBAAkB;AACrB,mBAAK,eAAe,WAAW,aAAa;AAC5C;YACF;YACA,KAAK,aAAa;AAChB,mBAAK,sBAAsB,YAAY,aAAa;AACpD;YACF;YACA,KAAK,cAAc;AACjB,oBAAM,QAAQ,cAAc,KAAK,QAAQ,cAAc,KAAK;AAC5D,mBAAK,KAAK,iBAAiB,UAAU;AACrC,oBAAM;YACR;YACA,KAAK;AACH;YACF,SAAS;AACP;YACF;UACF;AAEA,iBAAO;YACL,4BAA4B,KAAK,2BAA2B;UAC9D;QACF;QACA,yBAAyB,QAAQ;MACnC;MACA;MACA,KAAK;MACL,KAAK;IACP;AACA,SAAK,KAAK,yBAAyB;AAGnC,QAAI,QAAQ,YAAY;AACtB,kBAAY;IACd;EACF;;;;;;EAOQ,6BAA6B;AACnC,UAAM,6BACJ,KAAK,eAAe,2BAA2B,KAC/C,KAAK,MAAM,2BAA2B;AACxC,WAAO;EACT;EAEQ,kBAAkB,YAAgB;AACxC,QACE,KAAK,yBAAyB,UAC9B,KAAK,qBAAqB,gBAAgB,UAAU,GACpD;AACA,WAAK,uBAAuB;IAC9B;EACF;EAEA,0BAA0B;AACxB,WAAO,KAAK;EACd;;;;;;;;;EAUQ,2BACN,mBACA;AACA,UAAM,qBACJ,KAAK,eAAe,mBAAmB;AACzC,UAAM,oBAAqC,oBAAI,IAAI;AACnD,eAAW,CAAC,SAAS,MAAM,KAAK,oBAAoB;AAClD,YAAM,aAAa,KAAK,MAAM,WAAW,OAAO;AAIhD,UAAI,eAAe,MAAM;AACvB,cAAM,QAAQ;UACZ;UACA,SAAS,KAAK,MAAM,UAAU,OAAO;UACrC,MAAM,KAAK,MAAM,UAAU,OAAO;QACpC;AACA,0BAAkB,IAAI,YAAY,KAAK;MACzC;IACF;AAKA,UAAM,qBACJ,KAAK,uBAAuB;MAC1B;MACA,IAAI,IAAI,kBAAkB,KAAK,CAAC;IAClC;AAEF,SAAK,iBAAiB;MACpB,SAAS,mBAAmB,IAAI,CAAC,UAAU;AACzC,cAAM,mBACJ,KAAK,uBAAuB,eAAe,KAAK;AAClD,eAAO;UACL;UACA,cAAc;YACZ,MAAM;YACN,QAAQ,iBAAkB;UAC5B;QACF;MACF,CAAC;MACD,oBAAoB,MAAM,KAAK,iBAAiB,EAAE;QAChD,CAAC,CAAC,WAAW,MAAM,OAAO;UACxB;UACA;QACF;MACF;MACA,WAAW,KAAK,eAAe,UAAU;IAC3C,CAAC;EACH;EAEQ,iBAAiB,YAAwB;AAC/C,eAAW,MAAM,KAAK,iBAAiB,OAAO,GAAG;AAC/C,SAAG,UAAU;IACf;EACF;;;;;;;;;;EAWA,uBAAuB,IAAsC;AAC3D,UAAM,KAAK,KAAK;AAChB,SAAK,iBAAiB,IAAI,IAAI,EAAE;AAChC,WAAO,MAAM,KAAK,iBAAiB,OAAO,EAAE;EAC9C;;;;;;;;;EAUA,QACE,YACA,UACA;AACA,SAAK,KAAK,sBAAsB,UAAU,YAAY,QAAQ;EAChE;EAEA,UAAU;AACR,WAAO,KAAK,MAAM,QAAQ;EAC5B;;EAGA,aAAa,OAAe,kBAA2C;AACrE,UAAM,UAAU,KAAK,MAAM,aAAa,OAAO,gBAAgB;AAC/D,SAAK,iBAAiB,YAAY,OAAO;EAC3C;EAEA,YAAY;AACV,UAAM,UAAU,KAAK,MAAM,UAAU;AACrC,SAAK,iBAAiB,YAAY,OAAO;EAC3C;;;;;;;;;;;;;;;EAgBA,UACE,MACA,MACA,SACqD;AACrD,UAAM,aAAa,UAAU,IAAI;AAEjC,UAAM,EAAE,cAAc,YAAY,YAAY,IAAI,KAAK,MAAM;MAC3D;MACA;MACA,mCAAS;MACT,mCAAS;IACX;AACA,QAAI,iBAAiB,MAAM;AACzB,WAAK,iBAAiB,YAAY,YAAY;IAChD;AACA,WAAO;MACL;MACA,aAAa,MAAM;AACjB,cAAMC,gBAAe,YAAY;AACjC,YAAIA,eAAc;AAChB,eAAK,iBAAiB,YAAYA,aAAY;QAChD;MACF;IACF;EACF;;;;;;;EAQA,iBACE,SACA,MACmB;AACnB,UAAM,aAAa,UAAU,IAAI;AACjC,UAAM,aAAa,qBAAqB,SAAS,UAAU;AAC3D,WAAO,KAAK,uBAAuB,YAAY,UAAU;EAC3D;;;;;;;;;EAUA,wBAAwB,YAA2C;AACjE,WAAO,KAAK,uBAAuB,YAAY,UAAU;EAC3D;;;;;;;;EASA,2BAA2B,YAAiC;AAC1D,WAAO,KAAK,uBAAuB,eAAe,UAAU;EAC9D;;;;EAKA,eACE,SACA,MACsB;AACtB,UAAM,aAAa,UAAU,IAAI;AACjC,UAAM,aAAa,qBAAqB,SAAS,UAAU;AAC3D,WAAO,KAAK,uBAAuB,UAAU,UAAU;EACzD;;;;;;;;;;EAWA,aACE,MACA,MAC0B;AAC1B,UAAM,aAAa,UAAU,IAAI;AACjC,UAAM,aAAa,qBAAqB,MAAM,UAAU;AACxD,WAAO,KAAK,MAAM,aAAa,UAAU;EAC3C;;;;;;;EAQA,kBAAmC;AACjC,UAAM,oBAAoB,KAAK,iBAAiB,gBAAgB;AAChE,WAAO;MACL,qBAAqB,KAAK,eAAe,oBAAoB;MAC7D,sBAAsB,kBAAkB;MACxC,kBAAkB,kBAAkB;MACpC,iBAAiB,kBAAkB;MACnC,mBAAmB,kBAAkB;MACrC,6BACE,KAAK,eAAe,4BAA4B;MAClD,mBAAmB,KAAK,eAAe,kBAAkB;MACzD,iBAAiB,KAAK,eAAe,gBAAgB;IACvD;EACF;;;;;;;;;;;EAiCA,2BACE,IACY;AACZ,UAAM,KAAK,KAAK;AAChB,SAAK,2BAA2B,IAAI,IAAI,EAAE;AAC1C,WAAO,MAAM;AACX,WAAK,2BAA2B,OAAO,EAAE;IAC3C;EACF;;;;;;;;;;;EAYA,MAAM,SACJ,MACA,MACA,SACc;AACd,UAAM,SAAS,MAAM,KAAK,iBAAiB,MAAM,MAAM,OAAO;AAC9D,QAAI,CAAC,OAAO,SAAS;AACnB,UAAI,OAAO,cAAc,QAAW;AAClC,cAAM;UACJ;UACA,IAAI;YACF,4BAA4B,YAAY,MAAM,MAAM;UACtD;QACF;MACF;AACA,YAAM,IAAI,MAAM,4BAA4B,YAAY,MAAM,MAAM,CAAC;IACvE;AACA,WAAO,OAAO;EAChB;;;;EAKA,MAAM,iBACJ,SACA,MACA,SACA,eACyB;AACzB,UAAM,EAAE,gBAAgB,IAAI,KAAK;MAC/B;MACA;MACA;MACA;IACF;AACA,WAAO;EACT;;;;EAKA,gBACE,SACA,MACA,SACA,eACoE;AACpE,UAAM,eAAe,UAAU,IAAI;AACnC,SAAK,wBAAwB;AAC7B,UAAM,YAAY,KAAK;AACvB,SAAK;AAEL,QAAI,YAAY,QAAW;AACzB,YAAM,mBAAmB,QAAQ;AACjC,UAAI,qBAAqB,QAAW;AAClC,cAAM,gBAAgB,CAAC,oBAA0C;AAC/D,gBAAM,SAAkB;YACtB;YACA;UACF;AACA,cAAI,kBAAkB,SAAS;AAC7B,iBAAK,OAAO;cACV;YACF;UACF;QACF;AAEA,cAAM,qBACJ,KAAK,uBAAuB;UAC1B;UACA;QACF;AAEF,cAAM,iBAAiB,mBAAmB,IAAI,CAAC,UAAU;AACvD,gBAAM,cAAc,KAAK,wBAAwB,KAAK;AACtD,iBAAO;YACL;YACA,cAAc;cACZ,MAAM;cACN,QACE,gBAAgB,SACZ,SACA;gBACE,SAAS;gBACT,OAAO;gBACP,UAAU,CAAC;cACb;YACR;UACF;QACF,CAAC;AACD,aAAK,iBAAiB;UACpB,SAAS;UACT,oBAAoB,CAAC;UACrB,WAAW,KAAK,eAAe,UAAU;QAC3C,CAAC;MACH;IACF;AAEA,UAAM,UAA2B;MAC/B,MAAM;MACN;MACA;MACA;MACA,MAAM,CAAC,aAAa,YAAY,CAAC;IACnC;AACA,UAAM,cAAc,KAAK,iBAAiB,YAAY,OAAO;AAC7D,UAAM,kBAAkB,KAAK,eAAe,QAAQ,SAAS,WAAW;AACxE,WAAO;MACL;MACA;IACF;EACF;;;;;;;;;EAUA,MAAM,OAAO,MAAc,MAA4C;AACrE,UAAM,SAAS,MAAM,KAAK,eAAe,MAAM,IAAI;AACnD,QAAI,CAAC,OAAO,SAAS;AACnB,UAAI,OAAO,cAAc,QAAW;AAClC,cAAM;UACJ;UACA,IAAI,YAAY,4BAA4B,UAAU,MAAM,MAAM,CAAC;QACrE;MACF;AACA,YAAM,IAAI,MAAM,4BAA4B,UAAU,MAAM,MAAM,CAAC;IACrE;AACA,WAAO,OAAO;EAChB;;;;EAKA,MAAM,eACJ,SACA,MACA,eACyB;AACzB,UAAM,aAAa,UAAU,IAAI;AACjC,UAAM,YAAY,KAAK;AACvB,SAAK;AACL,SAAK,wBAAwB;AAE7B,UAAM,UAAyB;MAC7B,MAAM;MACN;MACA;MACA;MACA,MAAM,CAAC,aAAa,UAAU,CAAC;IACjC;AAEA,UAAM,cAAc,KAAK,iBAAiB,YAAY,OAAO;AAC7D,WAAO,KAAK,eAAe,QAAQ,SAAS,WAAW;EACzD;;;;;;;;;EAUA,MAAM,QAAuB;AAC3B,SAAK,sBAAsB,KAAK;AAChC,WAAO,KAAK,iBAAiB,UAAU;EACzC;;;;;;;EAQA,IAAI,MAAM;AACR,WAAO,KAAK;EACd;;;;EAKA,IAAI,gBAAgB;AAClB,WAAO,KAAK;EACd;;;;EAKA,IAAI,YAAY;AACd,WAAO,KAAK;EACd;;;;;EAaQ,cAAc;AACpB,QAAI,KAAK,OAAO;AACd,YAAM,SAAS,eAAe,KAAK,SAAS;AAC5C,WAAK,iBAAiB,YAAY;QAChC,MAAM;QACN,WAAW;QACX,OAAO;MACT,CAAC;IACH;EACF;EAEQ,0BAA0B;AAChC,QAAI,CAAC,KAAK,OAAO;AACf;IACF;AACA,UAAM,sBACJ,KAAK,gBAAgB,EAAE;AACzB,QACE,wBAAwB,QACxB,KAAK,IAAI,IAAI,oBAAoB,QAAQ,KAAK,KAAK,KACnD;AACA;IACF;AACA,UAAM,WAAW,GAAG,KAAK,OAAO;AAChC,UAAM,UAAU;MACd,QAAQ;MACR,SAAS;QACP,gBAAgB;QAChB,iBAAiB,OAAO,OAAO;MACjC;MACA,MAAM,KAAK,UAAU,EAAE,OAAO,0BAA0B,CAAC;IAC3D,CAAC,EACE,KAAK,CAAC,aAAa;AAClB,UAAI,CAAC,SAAS,IAAI;AAChB,aAAK,OAAO;UACV;UACA,SAAS;QACX;MACF;IACF,CAAC,EACA,MAAM,CAAC,UAAU;AAChB,WAAK,OAAO,KAAK,yCAAyC,KAAK;IACjE,CAAC;EACL;AACF;;;ACnjCA,IAAAC,gBAAwD;;;;AA+BxD,IAAM,kCAAkC;AAExC,IAAI,OAAO,cAAAC,YAAU,aAAa;AAChC,QAAM,IAAI,MAAM,uCAAuC;AACzD;AAgDO,SAAS,eACd,mBACA,QACA,QACoB;AACpB,WAAS,SAAS,MAAgD;AAChE,gCAA4B,IAAI;AAEhC,WAAO,OAAO,SAAS,mBAAmB,MAAM;MAC9C,kBAAkB;IACpB,CAAC;EACH;AACA,WAAS,uBAAuB,SAAS,qBACvC,kBACoB;AACpB,QAAI,WAAW,QAAW;AACxB,YAAM,IAAI;QACR,oDAAoD;UAClD;QACF,CAAC;MACH;IACF;AACA,WAAO,eAAe,mBAAmB,QAAQ,gBAAgB;EACnE;AACA,SAAO;AACT;AAkBA,SAAS,aACP,iBACA,QACkB;AAClB,SAAO,SAAU,MAAgD;AAC/D,WAAO,OAAO,OAAO,iBAAiB,IAAI;EAC5C;AACF;AAmGO,IAAM,oBAAN,MAAwB;;;;;;EAgB7B,YAAY,SAAiB,SAAoC;AAfjE,IAAAC,gBAAA,MAAQ,SAAA;AACR,IAAAA,gBAAA,MAAQ,YAAA;AACR,IAAAA,gBAAA,MAAQ,WAAA;AACR,IAAAA,gBAAA,MAAQ,SAAA;AACR,IAAAA,gBAAA,MAAQ,UAAS,KAAA;AACjB,IAAAA,gBAAA,MAAQ,SAAA;AAER,IAAAA,gBAAA,MAAQ,WAAA;AACR,IAAAA,gBAAA,MAAQ,kBAAA;AAUN,QAAI,YAAY,QAAW;AACzB,YAAM,IAAI;QACR;MAGF;IACF;AACA,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,IAAI;QACR,4GAA4G,OAAO,OAAO;MAC5H;IACF;AACA,QAAI,CAAC,QAAQ,SAAS,KAAK,GAAG;AAC5B,YAAM,IAAI,MAAM,2CAA2C;IAC7D;AACA,SAAK,UAAU;AACf,SAAK,YAAY,oBAAI,IAAI;AACzB,SAAK,WACH,mCAAS,YAAW,QAChB,sBAAsB,EAAE,UAAS,mCAAS,YAAW,MAAM,CAAC,KAC5D,mCAAS,YAAW,SAAQ,mCAAS,UACnC,QAAQ,SACR,yBAAyB,EAAE,UAAS,mCAAS,YAAW,MAAM,CAAC;AACvE,SAAK,UAAU,EAAE,GAAG,SAAS,QAAQ,KAAK,QAAQ;EACpD;;;;;;;EAQA,IAAI,MAAM;AACR,WAAO,KAAK;EACd;;;;;;;EAQA,IAAI,OAAO;AACT,QAAI,KAAK,QAAQ;AACf,YAAM,IAAI,MAAM,4CAA4C;IAC9D;AACA,QAAI,KAAK,YAAY;AACnB,aAAO,KAAK;IACd;AACA,SAAK,aAAa,IAAI;MACpB,KAAK;MACL,CAAC,mBAAmB,KAAK,WAAW,cAAc;MAClD,KAAK;IACP;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,WAAW,aAAa,KAAK,WAAW,KAAK,gBAAgB;IACpE;AACA,WAAO,KAAK;EACd;;;;;;;;;EAUA,QACE,YACA,UACA;AACA,QAAI,OAAO,eAAe,UAAU;AAClC,YAAM,IAAI;QACR;MAEF;IACF;AACA,SAAK,KAAK;MACR;MACA,aACG,MAAM;MAEP;IACJ;EACF;;;;EAKA,YAAY;AACV,SAAK,KAAK,UAAU;EACtB;;;;EAKA,aAAa,OAAe,UAAmC;AAC7D,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,QAAI,KAAK,QAAQ;AACf,YAAM,IAAI,MAAM,4CAA4C;IAC9D;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,KAAK,aAAa,OAAO,QAAQ;IACxC;EACF;;;;;;;;;;;;;;EAeA,WACE,UACG,gBAC+B;AAClC,UAAM,CAAC,MAAM,OAAO,IAAI;AACxB,UAAM,OAAO,gBAAgB,KAAK;AAClC,WAAO;MACL,UAAU,CAAC,aAAa;AACtB,cAAM,EAAE,YAAY,YAAY,IAAI,KAAK,KAAK;UAC5C;UACA;UACA;QACF;AAEA,cAAM,mBAAmB,KAAK,UAAU,IAAI,UAAU;AACtD,YAAI,qBAAqB,QAAW;AAClC,2BAAiB,IAAI,QAAQ;QAC/B,OAAO;AACL,eAAK,UAAU,IAAI,YAAY,oBAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpD;AAEA,eAAO,MAAM;AACX,cAAI,KAAK,QAAQ;AACf;UACF;AAEA,gBAAMC,oBAAmB,KAAK,UAAU,IAAI,UAAU;AACtDA,4BAAiB,OAAO,QAAQ;AAChC,cAAIA,kBAAiB,SAAS,GAAG;AAC/B,iBAAK,UAAU,OAAO,UAAU;UAClC;AACA,sBAAY;QACd;MACF;MAEA,kBAAkB,MAAM;AAGtB,YAAI,KAAK,YAAY;AACnB,iBAAO,KAAK,WAAW,iBAAiB,MAAM,IAAI;QACpD;AACA,eAAO;MACT;MAEA,gBAAgB,MAAM;AACpB,YAAI,KAAK,YAAY;AACnB,iBAAO,KAAK,WAAW,eAAe,MAAM,IAAI;QAClD;AACA,eAAO;MACT;MAEA,SAAS,MAAM;AACb,YAAI,KAAK,YAAY;AACnB,iBAAO,KAAK,WAAW,aAAa,MAAM,IAAI;QAChD;AACA,eAAO;MACT;IACF;EACF;;;;;;;;;;;;;;;;EAiBA,aACE,cAGA;AACA,UAAM,wBACJ,aAAa,yBAAyB;AACxC,UAAM,QAAQ,KAAK,WAAW,aAAa,OAAO,aAAa,QAAQ,CAAC,CAAC;AACzE,UAAM,cAAc,MAAM,SAAS,MAAM;IAAC,CAAC;AAC3C,eAAW,aAAa,qBAAqB;EAC/C;;;;;;;;;;;EAYA,SACE,aACG,gBAIoC;AACvC,UAAM,CAAC,MAAM,OAAO,IAAI;AACxB,UAAM,OAAO,gBAAgB,QAAQ;AACrC,WAAO,KAAK,KAAK,SAAS,MAAM,MAAM,OAAO;EAC/C;;;;;;;;;;EAWA,OACE,WACG,MACkC;AACrC,UAAM,OAAO,gBAAgB,MAAM;AACnC,WAAO,KAAK,KAAK,OAAO,MAAM,GAAG,IAAI;EACvC;;;;;;;;;;;;;EAcA,MACE,UACG,MACiC;AACpC,UAAM,QAAQ,KAAK,WAAW,OAAO,GAAG,IAAI;AAC5C,UAAM,iBAAiB,MAAM,iBAAiB;AAC9C,QAAI,mBAAmB,QAAW;AAChC,aAAO,QAAQ,QAAQ,cAAc;IACvC;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,cAAc,MAAM,SAAS,MAAM;AACvC,oBAAY;AACZ,YAAI;AACF,kBAAQ,MAAM,iBAAiB,CAAC;QAClC,SAAS,GAAG;AACV,iBAAO,CAAC;QACV;MACF,CAAC;IACH,CAAC;EACH;;;;;;;EAQA,kBAAmC;AACjC,WAAO,KAAK,KAAK,gBAAgB;EACnC;;;;;;;;;;;;;EAcA,2BACE,IACY;AACZ,WAAO,KAAK,KAAK,2BAA2B,EAAE;EAChD;;;;;;EAOA,IAAI,SAAiB;AACnB,WAAO,KAAK;EACd;;;;;;;;;EAUA,MAAM,QAAuB;AAC3B,SAAK,SAAS;AAEd,SAAK,YAAY,oBAAI,IAAI;AACzB,QAAI,KAAK,YAAY;AACnB,YAAM,OAAO,KAAK;AAClB,WAAK,aAAa;AAClB,YAAM,KAAK,MAAM;IACnB;EACF;EAEQ,WAAW,gBAA8B;AAC/C,eAAW,cAAc,gBAAgB;AACvC,YAAM,YAAY,KAAK,UAAU,IAAI,UAAU;AAC/C,UAAI,WAAW;AACb,mBAAW,YAAY,WAAW;AAChC,mBAAS;QACX;MACF;IACF;EACF;AACF;AAEA,IAAM,gBAAgB,cAAAF,QAAM;EAC1B;;AACF;AAWO,SAAS,YAA+B;AAC7C,aAAO,0BAAW,aAAa;AACjC;AAYO,IAAM,iBAGR,CAAC,EAAE,QAAQ,SAAS,MAAM;AAC7B,SAAO,cAAAA,QAAM;IACX,cAAc;IACd,EAAE,OAAO,OAAO;IAChB;EACF;AACF;AAuBO,SAAS,SACd,UACG,MAC+B;AAClC,QAAM,OAAO,KAAK,CAAC,MAAM;AACzB,QAAM,aAAa,KAAK,CAAC,MAAM,SAAS,CAAC,IAAI,UAAU,KAAK,CAAC,CAAC;AAE9D,QAAM,iBACJ,OAAO,UAAU,WACb,sBAAyC,KAAK,IAC9C;AAEN,QAAM,YAAY,gBAAgB,cAAc;AAEhD,QAAM,cAAU;IACd,MACE,OACK,CAAC,IACF,EAAE,OAAO,EAAE,OAAO,gBAAgB,MAAM,WAAW,EAAE;;;;IAI3D,CAAC,KAAK,UAAU,aAAa,UAAU,CAAC,GAAG,WAAW,IAAI;EAC5D;AAEA,QAAM,UAAU,WAAW,OAAO;AAClC,QAAM,SAAS,QAAQ,OAAO;AAC9B,MAAI,kBAAkB,OAAO;AAC3B,UAAM;EACR;AACA,SAAO;AACT;AAqBO,SAAS,YACd,UACyB;AACzB,QAAM,oBACJ,OAAO,aAAa,WAChB,sBAA4C,QAAQ,IACpD;AAEN,QAAM,aAAS,0BAAW,aAAa;AACvC,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI;MACR;IAGF;EACF;AACA,aAAO;IACL,MAAM,eAAe,mBAAmB,MAAM;;IAE9C,CAAC,QAAQ,gBAAgB,iBAAiB,CAAC;EAC7C;AACF;AAoBO,SAAS,UACd,QACqB;AACrB,QAAM,aAAS,0BAAW,aAAa;AACvC,QAAM,kBACJ,OAAO,WAAW,WACd,sBAA0C,MAAM,IAChD;AAEN,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI;MACR;IAGF;EACF;AACA,aAAO;IACL,MAAM,aAAa,iBAAiB,MAAM;;IAE1C,CAAC,QAAQ,gBAAgB,eAAe,CAAC;EAC3C;AACF;AAkBO,SAAS,2BAA4C;AAC1D,QAAM,aAAS,0BAAW,aAAa;AACvC,MAAI,WAAW,QAAW;AACxB,UAAM,IAAI;MACR;IAGF;EACF;AAEA,QAAM,sBAAkB,2BAAY,MAAM;AACxC,WAAO,OAAO,gBAAgB;EAChC,GAAG,CAAC,MAAM,CAAC;AAEX,QAAM,gBAAY;IAChB,CAAC,aAAyB;AACxB,aAAO,OAAO,2BAA2B,MAAM;AAC7C,iBAAS;MACX,CAAC;IACH;IACA,CAAC,MAAM;EACT;AAEA,SAAO,gBAAgB,EAAE,iBAAiB,UAAU,CAAC;AACvD;AAIA,SAAS,4BAA4B,OAAY;AAG/C,MACE,OAAO,UAAU,YACjB,UAAU,QACV,aAAa,SACb,aAAa,SACb,wBAAwB,OACxB;AACA,UAAM,IAAI;MACR;IACF;EACF;AACF;;;;;;ACzyBO,IAAM,kBAAN,MAAsB;EAK3B,YAAY,aAA0B;AAJtC,IAAAG,gBAAA,MAAO,aAAA;AACP,IAAAA,gBAAA,MAAQ,SAAA;AACR,IAAAA,gBAAA,MAAQ,WAAA;AAGN,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,SAAK,YAAY,oBAAI,IAAI;EAC3B;EAEA,WACE,YAIA;AAGA,eAAW,cAAc,OAAO,KAAK,UAAU,GAAG;AAChD,YAAM,EAAE,OAAO,KAAK,IAAI,WAAW,UAAU;AAE7C,sBAAgB,KAAK;AAErB,UAAI,KAAK,QAAQ,UAAU,MAAM,QAAW;AAE1C,aAAK,SAAS,YAAY,OAAO,IAAI;MACvC,OAAO;AACL,cAAM,eAAe,KAAK,QAAQ,UAAU;AAC5C,YACE,gBAAgB,KAAK,MAAM,gBAAgB,aAAa,KAAK,KAC7D,KAAK,UAAU,aAAa,IAAI,CAAC,MAC/B,KAAK,UAAU,aAAa,aAAa,IAAI,CAAC,GAChD;AAEA,eAAK,YAAY,UAAU;AAC3B,eAAK,SAAS,YAAY,OAAO,IAAI;QACvC;MACF;IACF;AAGA,eAAW,cAAc,OAAO,KAAK,KAAK,OAAO,GAAG;AAClD,UAAI,WAAW,UAAU,MAAM,QAAW;AACxC,aAAK,YAAY,UAAU;MAC7B;IACF;EACF;EAEA,UAAU,UAAkC;AAC1C,SAAK,UAAU,IAAI,QAAQ;AAC3B,WAAO,MAAM;AACX,WAAK,UAAU,OAAO,QAAQ;IAChC;EACF;EAEA,gBACE,SAI+C;AAC/C,UAAM,SAAwD,CAAC;AAC/D,eAAW,cAAc,OAAO,KAAK,OAAO,GAAG;AAC7C,YAAM,EAAE,OAAO,KAAK,IAAI,QAAQ,UAAU;AAE1C,sBAAgB,KAAK;AAIrB,YAAM,QAAQ,KAAK,YAAY,OAAO,IAAI;AAC1C,UAAI;AACJ,UAAI;AACF,gBAAQ,MAAM,iBAAiB;MACjC,SAAS,GAAG;AAGV,YAAI,aAAa,OAAO;AACtB,kBAAQ;QACV,OAAO;AACL,gBAAM;QACR;MACF;AACA,aAAO,UAAU,IAAI;IACvB;AACA,WAAO;EACT;EAEA,eAAe,aAA0B;AACvC,SAAK,cAAc;AAGnB,eAAW,cAAc,OAAO,KAAK,KAAK,OAAO,GAAG;AAClD,YAAM,EAAE,OAAO,MAAM,MAAM,IAAI,KAAK,QAAQ,UAAU;AACtD,YAAM,UAAU,MAAM,QAAQ;AAC9B,WAAK,YAAY,UAAU;AAC3B,WAAK,SAAS,YAAY,OAAO,MAAM,OAAO;IAChD;EACF;EAEA,UAAU;AACR,eAAW,cAAc,OAAO,KAAK,KAAK,OAAO,GAAG;AAClD,WAAK,YAAY,UAAU;IAC7B;AACA,SAAK,YAAY,oBAAI,IAAI;EAC3B;EAEQ,SACN,YACA,OACA,MACA,SACA;AACA,QAAI,KAAK,QAAQ,UAAU,MAAM,QAAW;AAC1C,YAAM,IAAI;QACR,4CAA4C,UAAU;MACxD;IACF;AACA,UAAM,QAAQ,KAAK,YAAY,OAAO,MAAM,OAAO;AACnD,UAAM,cAAc,MAAM,SAAS,MAAM,KAAK,gBAAgB,CAAC;AAC/D,SAAK,QAAQ,UAAU,IAAI;MACzB;MACA;MACA;MACA;IACF;EACF;EAEQ,YAAY,YAAwB;AAC1C,UAAM,OAAO,KAAK,QAAQ,UAAU;AACpC,QAAI,SAAS,QAAW;AACtB,YAAM,IAAI,MAAM,kCAAkC,UAAU,GAAG;IACjE;AACA,SAAK,YAAY;AACjB,WAAO,KAAK,QAAQ,UAAU;EAChC;EAEQ,kBAAwB;AAC9B,eAAW,YAAY,KAAK,WAAW;AACrC,eAAS;IACX;EACF;AACF;;;AhB5GO,SAAS,WACd,SACyC;AACzC,QAAM,SAAS,UAAU;AACzB,MAAI,WAAW,QAAW;AAGxB,UAAM,IAAI;MACR;IAGF;EACF;AACA,QAAM,kBAAc,uBAAQ,MAAM;AAChC,WAAO,CACL,OACA,MACA,YACG;AACH,aAAO,OAAO,WAAW,OAAO,MAAM,EAAE,QAAQ,CAAC;IACnD;EACF,GAAG,CAAC,MAAM,CAAC;AACX,SAAO,iBAAiB,SAAS,WAAW;AAC9C;AAKO,SAAS,iBACd,SACA,aACyC;AACzC,QAAM,CAAC,QAAQ,QAAI,wBAAS,MAAM,IAAI,gBAAgB,WAAW,CAAC;AAElE,MAAI,SAAS,gBAAgB,aAAa;AACxC,aAAS,eAAe,WAAW;EACrC;AAGA,+BAAU,MAAM,MAAM,SAAS,QAAQ,GAAG,CAAC,QAAQ,CAAC;AAEpD,QAAM,mBAAe;IACnB,OAAO;MACL,iBAAiB,MAAM;AACrB,eAAO,SAAS,gBAAgB,OAAO;MACzC;MACA,WAAW,CAAC,aAAyB;AACnC,iBAAS,WAAW,OAAO;AAC3B,eAAO,SAAS,UAAU,QAAQ;MACpC;IACF;IACA,CAAC,UAAU,OAAO;EACpB;AAEA,SAAO,gBAAgB,YAAY;AACrC;;;AiBlHA,IAAAC,gBAMO;AAwBP,IAAM,wBAAoB,6BAA+B,MAAgB;AAYlE,SAAS,gBAGd;AACA,QAAM,kBAAc,0BAAW,iBAAiB;AAChD,MAAI,gBAAgB,QAAW;AAC7B,UAAM,IAAI;MACR;IAKF;EACF;AACA,SAAO;AACT;AAiBO,SAAS,uBAAuB;EACrC;EACA;EACA;AACF,GAUG;AACD,QAAM;IACJ,WAAW;IACX,iBAAiB;IACjB;EACF,IAAI,QAAQ;AACZ,QAAM,CAAC,uBAAuB,wBAAwB,QAAI,wBAExD,IAAI;AAMN,MAAI,uBAAuB,0BAA0B,MAAM;AACzD,6BAAyB,IAAI;EAC/B;AAGA,MACE,CAAC,uBACD,CAAC,6BACD,0BAA0B,OAC1B;AACA,6BAAyB,KAAK;EAChC;AAEA,SACE,cAAAC,QAAA;IAAC,kBAAkB;IAAlB;MACC,OAAO;QACL,WAAW,0BAA0B;QACrC,iBACE,8BAA8B,yBAAyB;MAC3D;IAAA;IAEA,cAAAA,QAAA;MAAC;MAAA;QACC;QACA;QACA;QACA;QACA;MAAA;IACF;IACA,cAAAA,QAAA,cAAC,gBAAA,EAAe,OAAA,GAAwB,QAAS;IACjD,cAAAA,QAAA;MAAC;MAAA;QACC;QACA;QACA;QACA;QACA;MAAA;IACF;EACF;AAEJ;AAIA,SAAS,2BAA2B;EAClC;EACA;EACA;EACA;EACA;AACF,GAUG;AACD,+BAAU,MAAM;AACd,QAAI,uBAAuB;AAC3B,QAAI,2BAA2B;AAC7B,aAAO,QAAQ,kBAAkB,CAAC,kCAAkC;AAClE,YAAI,sBAAsB;AACxB,mCAAyB,MAAM,6BAA6B;QAC9D;MACF,CAAC;AACD,aAAO,MAAM;AACX,+BAAuB;AAIvB;UAAyB,CAAC,0BACxB,wBAAwB,QAAQ;QAClC;MACF;IACF;EACF,GAAG;IACD;IACA;IACA;IACA;IACA;EACF,CAAC;AACD,SAAO;AACT;AAKA,SAAS,0BAA0B;EACjC;EACA;EACA;EACA;EACA;AACF,GAUG;AACD,+BAAU,MAAM;AAEd,QAAI,2BAA2B;AAC7B,aAAO,MAAM;AACX,eAAO,UAAU;AAOjB,iCAAyB,MAAM,IAAI;MACrC;IACF;EACF,GAAG;IACD;IACA;IACA;IACA;IACA;EACF,CAAC;AACD,SAAO;AACT;", "names": ["import_react", "__publicField", "version", "__publicField", "__publicField", "__publicField", "__publicField", "__publicField", "__publicField", "mark", "__publicField", "modification", "import_react", "React", "__publicField", "currentListeners", "__publicField", "import_react", "React"]}