{"version": 3, "sources": ["../../convex/src/react/use_paginated_query.ts", "../../convex/src/react/auth_helpers.tsx", "../../convex/src/react/hydration.tsx"], "sourcesContent": ["import { useMemo, useState } from \"react\";\n\nimport { OptimisticLocalStore } from \"../browser/index.js\";\nimport {\n  FunctionReturnType,\n  PaginationOptions,\n  paginationOptsValidator,\n  PaginationResult,\n} from \"../server/index.js\";\nimport { ConvexError, convexTo<PERSON>son, Infer, Value } from \"../values/index.js\";\nimport { useQueries } from \"./use_queries.js\";\nimport {\n  FunctionArgs,\n  FunctionReference,\n  getFunctionName,\n} from \"../server/api.js\";\nimport { BetterOmit, Expand } from \"../type_utils.js\";\nimport { useConvex } from \"./client.js\";\nimport { compareValues } from \"../values/compare.js\";\n\n/**\n * A {@link server.FunctionReference} that is usable with {@link usePaginatedQuery}.\n *\n * This function reference must:\n * - Refer to a public query\n * - Have an argument named \"paginationOpts\" of type {@link server.PaginationOptions}\n * - Have a return type of {@link server.PaginationResult}.\n *\n * @public\n */\nexport type PaginatedQueryReference = FunctionReference<\n  \"query\",\n  \"public\",\n  { paginationOpts: PaginationOptions },\n  PaginationResult<any>\n>;\n\n// Incrementing integer for each page queried in the usePaginatedQuery hook.\ntype QueryPageKey = number;\n\ntype UsePaginatedQueryState = {\n  query: FunctionReference<\"query\">;\n  args: Record<string, Value>;\n  id: number;\n  nextPageKey: QueryPageKey;\n  pageKeys: QueryPageKey[];\n  queries: Record<\n    QueryPageKey,\n    {\n      query: FunctionReference<\"query\">;\n      // Use the validator type as a test that it matches the args\n      // we generate.\n      args: { paginationOpts: Infer<typeof paginationOptsValidator> };\n    }\n  >;\n  ongoingSplits: Record<QueryPageKey, [QueryPageKey, QueryPageKey]>;\n  skip: boolean;\n};\n\nconst splitQuery =\n  (key: QueryPageKey, splitCursor: string, continueCursor: string) =>\n  (prevState: UsePaginatedQueryState) => {\n    const queries = { ...prevState.queries };\n    const splitKey1 = prevState.nextPageKey;\n    const splitKey2 = prevState.nextPageKey + 1;\n    const nextPageKey = prevState.nextPageKey + 2;\n    queries[splitKey1] = {\n      query: prevState.query,\n      args: {\n        ...prevState.args,\n        paginationOpts: {\n          ...prevState.queries[key].args.paginationOpts,\n          endCursor: splitCursor,\n        },\n      },\n    };\n    queries[splitKey2] = {\n      query: prevState.query,\n      args: {\n        ...prevState.args,\n        paginationOpts: {\n          ...prevState.queries[key].args.paginationOpts,\n          cursor: splitCursor,\n          endCursor: continueCursor,\n        },\n      },\n    };\n    const ongoingSplits = { ...prevState.ongoingSplits };\n    ongoingSplits[key] = [splitKey1, splitKey2];\n    return {\n      ...prevState,\n      nextPageKey,\n      queries,\n      ongoingSplits,\n    };\n  };\n\nconst completeSplitQuery =\n  (key: QueryPageKey) => (prevState: UsePaginatedQueryState) => {\n    const completedSplit = prevState.ongoingSplits[key];\n    if (completedSplit === undefined) {\n      return prevState;\n    }\n    const queries = { ...prevState.queries };\n    delete queries[key];\n    const ongoingSplits = { ...prevState.ongoingSplits };\n    delete ongoingSplits[key];\n    let pageKeys = prevState.pageKeys.slice();\n    const pageIndex = prevState.pageKeys.findIndex((v) => v === key);\n    if (pageIndex >= 0) {\n      pageKeys = [\n        ...prevState.pageKeys.slice(0, pageIndex),\n        ...completedSplit,\n        ...prevState.pageKeys.slice(pageIndex + 1),\n      ];\n    }\n    return {\n      ...prevState,\n      queries,\n      pageKeys,\n      ongoingSplits,\n    };\n  };\n\n/**\n * Load data reactively from a paginated query to a create a growing list.\n *\n * This can be used to power \"infinite scroll\" UIs.\n *\n * This hook must be used with public query references that match\n * {@link PaginatedQueryReference}.\n *\n * `usePaginatedQuery` concatenates all the pages of results into a single list\n * and manages the continuation cursors when requesting more items.\n *\n * Example usage:\n * ```typescript\n * const { results, status, isLoading, loadMore } = usePaginatedQuery(\n *   api.messages.list,\n *   { channel: \"#general\" },\n *   { initialNumItems: 5 }\n * );\n * ```\n *\n * If the query reference or arguments change, the pagination state will be reset\n * to the first page. Similarly, if any of the pages result in an InvalidCursor\n * error or an error associated with too much data, the pagination state will also\n * reset to the first page.\n *\n * To learn more about pagination, see [Paginated Queries](https://docs.convex.dev/database/pagination).\n *\n * @param query - A FunctionReference to the public query function to run.\n * @param args - The arguments object for the query function, excluding\n * the `paginationOpts` property. That property is injected by this hook.\n * @param options - An object specifying the `initialNumItems` to be loaded in\n * the first page.\n * @returns A {@link UsePaginatedQueryResult} that includes the currently loaded\n * items, the status of the pagination, and a `loadMore` function.\n *\n * @public\n */\nexport function usePaginatedQuery<Query extends PaginatedQueryReference>(\n  query: Query,\n  args: PaginatedQueryArgs<Query> | \"skip\",\n  options: { initialNumItems: number },\n): UsePaginatedQueryReturnType<Query> {\n  if (\n    typeof options?.initialNumItems !== \"number\" ||\n    options.initialNumItems < 0\n  ) {\n    throw new Error(\n      `\\`options.initialNumItems\\` must be a positive number. Received \\`${options?.initialNumItems}\\`.`,\n    );\n  }\n  const skip = args === \"skip\";\n  const argsObject = skip ? {} : args;\n  const queryName = getFunctionName(query);\n  const createInitialState = useMemo(() => {\n    return () => {\n      const id = nextPaginationId();\n      return {\n        query,\n        args: argsObject as Record<string, Value>,\n        id,\n        nextPageKey: 1,\n        pageKeys: skip ? [] : [0],\n        queries: skip\n          ? ({} as UsePaginatedQueryState[\"queries\"])\n          : {\n              0: {\n                query,\n                args: {\n                  ...argsObject,\n                  paginationOpts: {\n                    numItems: options.initialNumItems,\n                    cursor: null,\n                    id,\n                  },\n                },\n              },\n            },\n        ongoingSplits: {},\n        skip,\n      };\n    };\n    // ESLint doesn't like that we're stringifying the args. We do this because\n    // we want to avoid rerendering if the args are a different\n    // object that serializes to the same result.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    JSON.stringify(convexToJson(argsObject as Value)),\n    queryName,\n    options.initialNumItems,\n    skip,\n  ]);\n\n  const [state, setState] =\n    useState<UsePaginatedQueryState>(createInitialState);\n\n  // `currState` is the state that we'll render based on.\n  let currState = state;\n  if (\n    getFunctionName(query) !== getFunctionName(state.query) ||\n    JSON.stringify(convexToJson(argsObject as Value)) !==\n      JSON.stringify(convexToJson(state.args)) ||\n    skip !== state.skip\n  ) {\n    currState = createInitialState();\n    setState(currState);\n  }\n  const convexClient = useConvex();\n  const logger = convexClient.logger;\n\n  const resultsObject = useQueries(currState.queries);\n\n  const [results, maybeLastResult]: [\n    Value[],\n    undefined | PaginationResult<Value>,\n  ] = useMemo(() => {\n    let currResult = undefined;\n\n    const allItems = [];\n    for (const pageKey of currState.pageKeys) {\n      currResult = resultsObject[pageKey];\n      if (currResult === undefined) {\n        break;\n      }\n\n      if (currResult instanceof Error) {\n        if (\n          currResult.message.includes(\"InvalidCursor\") ||\n          (currResult instanceof ConvexError &&\n            typeof currResult.data === \"object\" &&\n            currResult.data?.isConvexSystemError === true &&\n            currResult.data?.paginationError === \"InvalidCursor\")\n        ) {\n          // - InvalidCursor: If the cursor is invalid, probably the paginated\n          // database query was data-dependent and changed underneath us. The\n          // cursor in the params or journal no longer matches the current\n          // database query.\n\n          // In all cases, we want to restart pagination to throw away all our\n          // existing cursors.\n          logger.warn(\n            \"usePaginatedQuery hit error, resetting pagination state: \" +\n              currResult.message,\n          );\n          setState(createInitialState);\n          return [[], undefined];\n        } else {\n          throw currResult;\n        }\n      }\n      const ongoingSplit = currState.ongoingSplits[pageKey];\n      if (ongoingSplit !== undefined) {\n        if (\n          resultsObject[ongoingSplit[0]] !== undefined &&\n          resultsObject[ongoingSplit[1]] !== undefined\n        ) {\n          // Both pages of the split have results now. Swap them in.\n          setState(completeSplitQuery(pageKey));\n        }\n      } else if (\n        currResult.splitCursor &&\n        (currResult.pageStatus === \"SplitRecommended\" ||\n          currResult.pageStatus === \"SplitRequired\" ||\n          currResult.page.length > options.initialNumItems * 2)\n      ) {\n        // If a single page has more than double the expected number of items,\n        // or if the server requests a split, split the page into two.\n        setState(\n          splitQuery(\n            pageKey,\n            currResult.splitCursor,\n            currResult.continueCursor,\n          ),\n        );\n      }\n      if (currResult.pageStatus === \"SplitRequired\") {\n        // If pageStatus is 'SplitRequired', it means the server was not able to\n        // fetch the full page. So we stop results before the incomplete\n        // page and return 'LoadingMore' while the page is splitting.\n        return [allItems, undefined];\n      }\n      allItems.push(...currResult.page);\n    }\n    return [allItems, currResult];\n  }, [\n    resultsObject,\n    currState.pageKeys,\n    currState.ongoingSplits,\n    options.initialNumItems,\n    createInitialState,\n    logger,\n  ]);\n\n  const statusObject = useMemo(() => {\n    if (maybeLastResult === undefined) {\n      if (currState.nextPageKey === 1) {\n        return {\n          status: \"LoadingFirstPage\",\n          isLoading: true,\n          loadMore: (_numItems: number) => {\n            // Intentional noop.\n          },\n        } as const;\n      } else {\n        return {\n          status: \"LoadingMore\",\n          isLoading: true,\n          loadMore: (_numItems: number) => {\n            // Intentional noop.\n          },\n        } as const;\n      }\n    }\n    if (maybeLastResult.isDone) {\n      return {\n        status: \"Exhausted\",\n        isLoading: false,\n        loadMore: (_numItems: number) => {\n          // Intentional noop.\n        },\n      } as const;\n    }\n    const continueCursor = maybeLastResult.continueCursor;\n    let alreadyLoadingMore = false;\n    return {\n      status: \"CanLoadMore\",\n      isLoading: false,\n      loadMore: (numItems: number) => {\n        if (!alreadyLoadingMore) {\n          alreadyLoadingMore = true;\n          setState((prevState) => {\n            const pageKeys = [...prevState.pageKeys, prevState.nextPageKey];\n            const queries = { ...prevState.queries };\n            queries[prevState.nextPageKey] = {\n              query: prevState.query,\n              args: {\n                ...prevState.args,\n                paginationOpts: {\n                  numItems,\n                  cursor: continueCursor,\n                  id: prevState.id,\n                },\n              },\n            };\n            return {\n              ...prevState,\n              nextPageKey: prevState.nextPageKey + 1,\n              pageKeys,\n              queries,\n            };\n          });\n        }\n      },\n    } as const;\n  }, [maybeLastResult, currState.nextPageKey]);\n\n  return {\n    results,\n    ...statusObject,\n  };\n}\n\nlet paginationId = 0;\n/**\n * Generate a new, unique ID for a pagination session.\n *\n * Every usage of {@link usePaginatedQuery} puts a unique ID into the\n * query function arguments as a \"cache-buster\". This serves two purposes:\n *\n * 1. All calls to {@link usePaginatedQuery} have independent query\n * journals.\n *\n * Every time we start a new pagination session, we'll load the first page of\n * results and receive a fresh journal. Without the ID, we might instead reuse\n * a query subscription already present in our client. This isn't desirable\n * because the existing query function result may have grown or shrunk from the\n * requested `initialNumItems`.\n *\n * 2. We can restart the pagination session on some types of errors.\n *\n * Sometimes we want to restart pagination from the beginning if we hit an error.\n * Similar to (1), we'd like to ensure that this new session actually requests\n * its first page from the server and doesn't reuse a query result already\n * present in the client that may have hit the error.\n *\n * @returns The pagination ID.\n */\nfunction nextPaginationId(): number {\n  paginationId++;\n  return paginationId;\n}\n\n/**\n * Reset pagination id for tests only, so tests know what it is.\n */\nexport function resetPaginationId() {\n  paginationId = 0;\n}\n\n/**\n * The result of calling the {@link usePaginatedQuery} hook.\n *\n * This includes:\n * - `results` - An array of the currently loaded results.\n * - `isLoading` - Whether the hook is currently loading results.\n * - `status` - The status of the pagination. The possible statuses are:\n *   - \"LoadingFirstPage\": The hook is loading the first page of results.\n *   - \"CanLoadMore\": This query may have more items to fetch. Call `loadMore` to\n *   fetch another page.\n *   - \"LoadingMore\": We're currently loading another page of results.\n *   - \"Exhausted\": We've paginated to the end of the list.\n * - `loadMore(n)` A callback to fetch more results. This will only fetch more\n * results if the status is \"CanLoadMore\".\n *\n * @public\n */\nexport type UsePaginatedQueryResult<Item> = {\n  results: Item[];\n  loadMore: (numItems: number) => void;\n} & (\n  | {\n      status: \"LoadingFirstPage\";\n      isLoading: true;\n    }\n  | {\n      status: \"CanLoadMore\";\n      isLoading: false;\n    }\n  | {\n      status: \"LoadingMore\";\n      isLoading: true;\n    }\n  | {\n      status: \"Exhausted\";\n      isLoading: false;\n    }\n);\n\n/**\n * The possible pagination statuses in {@link UsePaginatedQueryResult}.\n *\n * This is a union of string literal types.\n * @public\n */\nexport type PaginationStatus = UsePaginatedQueryResult<any>[\"status\"];\n\n/**\n * Given a {@link PaginatedQueryReference}, get the type of the arguments\n * object for the query, excluding the `paginationOpts` argument.\n *\n * @public\n */\nexport type PaginatedQueryArgs<Query extends PaginatedQueryReference> = Expand<\n  BetterOmit<FunctionArgs<Query>, \"paginationOpts\">\n>;\n\n/**\n * Given a {@link PaginatedQueryReference}, get the type of the item being\n * paginated over.\n * @public\n */\nexport type PaginatedQueryItem<Query extends PaginatedQueryReference> =\n  FunctionReturnType<Query>[\"page\"][number];\n\n/**\n * The return type of {@link usePaginatedQuery}.\n *\n * @public\n */\nexport type UsePaginatedQueryReturnType<Query extends PaginatedQueryReference> =\n  UsePaginatedQueryResult<PaginatedQueryItem<Query>>;\n\n/**\n * Optimistically update the values in a paginated list.\n *\n * This optimistic update is designed to be used to update data loaded with\n * {@link usePaginatedQuery}. It updates the list by applying\n * `updateValue` to each element of the list across all of the loaded pages.\n *\n * This will only apply to queries with a matching names and arguments.\n *\n * Example usage:\n * ```ts\n * const myMutation = useMutation(api.myModule.myMutation)\n * .withOptimisticUpdate((localStore, mutationArg) => {\n *\n *   // Optimistically update the document with ID `mutationArg`\n *   // to have an additional property.\n *\n *   optimisticallyUpdateValueInPaginatedQuery(\n *     localStore,\n *     api.myModule.paginatedQuery\n *     {},\n *     currentValue => {\n *       if (mutationArg === currentValue._id) {\n *         return {\n *           ...currentValue,\n *           \"newProperty\": \"newValue\",\n *         };\n *       }\n *       return currentValue;\n *     }\n *   );\n *\n * });\n * ```\n *\n * @param localStore - An {@link OptimisticLocalStore} to update.\n * @param query - A {@link FunctionReference} for the paginated query to update.\n * @param args - The arguments object to the query function, excluding the\n * `paginationOpts` property.\n * @param updateValue - A function to produce the new values.\n *\n * @public\n */\nexport function optimisticallyUpdateValueInPaginatedQuery<\n  Query extends PaginatedQueryReference,\n>(\n  localStore: OptimisticLocalStore,\n  query: Query,\n  args: PaginatedQueryArgs<Query>,\n  updateValue: (\n    currentValue: PaginatedQueryItem<Query>,\n  ) => PaginatedQueryItem<Query>,\n): void {\n  const expectedArgs = JSON.stringify(convexToJson(args as Value));\n\n  for (const queryResult of localStore.getAllQueries(query)) {\n    if (queryResult.value !== undefined) {\n      const { paginationOpts: _, ...innerArgs } = queryResult.args as {\n        paginationOpts: PaginationOptions;\n      };\n      if (JSON.stringify(convexToJson(innerArgs as Value)) === expectedArgs) {\n        const value = queryResult.value;\n        if (\n          typeof value === \"object\" &&\n          value !== null &&\n          Array.isArray(value.page)\n        ) {\n          localStore.setQuery(query, queryResult.args, {\n            ...value,\n            page: value.page.map(updateValue),\n          });\n        }\n      }\n    }\n  }\n}\n\n/**\n * Updates a paginated query to insert an element at the top of the list.\n *\n * This is regardless of the sort order, so if the list is in descending order,\n * the inserted element will be treated as the \"biggest\" element, but if it's\n * ascending, it'll be treated as the \"smallest\".\n *\n * Example:\n * ```ts\n * const createTask = useMutation(api.tasks.create)\n *   .withOptimisticUpdate((localStore, mutationArgs) => {\n *   insertAtTop({\n *     paginatedQuery: api.tasks.list,\n *     argsToMatch: { listId: mutationArgs.listId },\n *     localQueryStore: localStore,\n *     item: { _id: crypto.randomUUID() as Id<\"tasks\">, title: mutationArgs.title, completed: false },\n *   });\n * });\n * ```\n *\n * @param options.paginatedQuery - A function reference to the paginated query.\n * @param options.argsToMatch - Optional arguments that must be in each relevant paginated query.\n * This is useful if you use the same query function with different arguments to load\n * different lists.\n * @param options.localQueryStore\n * @param options.item The item to insert.\n * @returns\n */\nexport function insertAtTop<Query extends PaginatedQueryReference>(options: {\n  paginatedQuery: Query;\n  argsToMatch?: Partial<PaginatedQueryArgs<Query>>;\n  localQueryStore: OptimisticLocalStore;\n  item: PaginatedQueryItem<Query>;\n}) {\n  const { paginatedQuery, argsToMatch, localQueryStore, item } = options;\n  const queries = localQueryStore.getAllQueries(paginatedQuery);\n  const queriesThatMatch = queries.filter((q) => {\n    if (argsToMatch === undefined) {\n      return true;\n    }\n    return Object.keys(argsToMatch).every(\n      // @ts-expect-error -- This should be safe since both should be plain objects\n      (k) => compareValues(argsToMatch[k], q.args[k]) === 0,\n    );\n  });\n  const firstPage = queriesThatMatch.find(\n    (q) => q.args.paginationOpts.cursor === null,\n  );\n  if (firstPage === undefined || firstPage.value === undefined) {\n    // first page is not loaded, so don't update it until it loads\n    return;\n  }\n  localQueryStore.setQuery(paginatedQuery, firstPage.args, {\n    ...firstPage.value,\n    page: [item, ...firstPage.value.page],\n  });\n}\n\n/**\n * Updates a paginated query to insert an element at the bottom of the list.\n *\n * This is regardless of the sort order, so if the list is in descending order,\n * the inserted element will be treated as the \"smallest\" element, but if it's\n * ascending, it'll be treated as the \"biggest\".\n *\n * This only has an effect if the last page is loaded, since otherwise it would result\n * in the element being inserted at the end of whatever is loaded (which is the middle of the list)\n * and then popping out once the optimistic update is over.\n *\n * @param options.paginatedQuery - A function reference to the paginated query.\n * @param options.argsToMatch - Optional arguments that must be in each relevant paginated query.\n * This is useful if you use the same query function with different arguments to load\n * different lists.\n * @param options.localQueryStore\n * @param options.element The element to insert.\n * @returns\n */\nexport function insertAtBottomIfLoaded<\n  Query extends PaginatedQueryReference,\n>(options: {\n  paginatedQuery: Query;\n  argsToMatch?: Partial<PaginatedQueryArgs<Query>>;\n  localQueryStore: OptimisticLocalStore;\n  item: PaginatedQueryItem<Query>;\n}) {\n  const { paginatedQuery, localQueryStore, item, argsToMatch } = options;\n  const queries = localQueryStore.getAllQueries(paginatedQuery);\n  const queriesThatMatch = queries.filter((q) => {\n    if (argsToMatch === undefined) {\n      return true;\n    }\n    return Object.keys(argsToMatch).every(\n      // @ts-expect-error -- This should be safe since both should be plain objects\n      (k) => compareValues(argsToMatch[k], q.args[k]) === 0,\n    );\n  });\n  const lastPage = queriesThatMatch.find(\n    (q) => q.value !== undefined && q.value.isDone,\n  );\n  if (lastPage === undefined) {\n    // last page is not loaded, so don't update it since the item would immediately pop out\n    // when the server updates\n    return;\n  }\n  localQueryStore.setQuery(paginatedQuery, lastPage.args, {\n    ...lastPage.value!,\n    page: [...lastPage.value!.page, item],\n  });\n}\n\ntype LocalQueryResult<Query extends FunctionReference<\"query\">> = {\n  args: FunctionArgs<Query>;\n  value: undefined | FunctionReturnType<Query>;\n};\n\ntype LoadedResult<Query extends FunctionReference<\"query\">> = {\n  args: FunctionArgs<Query>;\n  value: FunctionReturnType<Query>;\n};\n\n/**\n * This is a helper function for inserting an item at a specific position in a paginated query.\n *\n * You must provide the sortOrder and a function for deriving the sort key (an array of values) from an item in the list.\n *\n * This will only work if the server query uses the same sort order and sort key as the optimistic update.\n *\n * Example:\n * ```ts\n * const createTask = useMutation(api.tasks.create)\n *   .withOptimisticUpdate((localStore, mutationArgs) => {\n *   insertAtPosition({\n *     paginatedQuery: api.tasks.listByPriority,\n *     argsToMatch: { listId: mutationArgs.listId },\n *     sortOrder: \"asc\",\n *     sortKeyFromItem: (item) => [item.priority, item._creationTime],\n *     localQueryStore: localStore,\n *     item: {\n *       _id: crypto.randomUUID() as Id<\"tasks\">,\n *       _creationTime: Date.now(),\n *       title: mutationArgs.title,\n *       completed: false,\n *       priority: mutationArgs.priority,\n *     },\n *   });\n * });\n * ```\n * @param options.paginatedQuery - A function reference to the paginated query.\n * @param options.argsToMatch - Optional arguments that must be in each relevant paginated query.\n * This is useful if you use the same query function with different arguments to load\n * different lists.\n * @param options.sortOrder - The sort order of the paginated query (\"asc\" or \"desc\").\n * @param options.sortKeyFromItem - A function for deriving the sort key (an array of values) from an element in the list.\n * Including a tie-breaker field like `_creationTime` is recommended.\n * @param options.localQueryStore\n * @param options.item - The item to insert.\n * @returns\n */\nexport function insertAtPosition<\n  Query extends PaginatedQueryReference,\n>(options: {\n  paginatedQuery: Query;\n  argsToMatch?: Partial<PaginatedQueryArgs<Query>>;\n  sortOrder: \"asc\" | \"desc\";\n  sortKeyFromItem: (element: PaginatedQueryItem<Query>) => Value | Value[];\n  localQueryStore: OptimisticLocalStore;\n  item: PaginatedQueryItem<Query>;\n}) {\n  const {\n    paginatedQuery,\n    sortOrder,\n    sortKeyFromItem,\n    localQueryStore,\n    item,\n    argsToMatch,\n  } = options;\n\n  const queries: LocalQueryResult<Query>[] =\n    localQueryStore.getAllQueries(paginatedQuery);\n  // Group into sets of pages for the same usePaginatedQuery. Grouping is by all\n  // args except paginationOpts, but including paginationOpts.id.\n  const queryGroups: Record<string, LocalQueryResult<Query>[]> = {};\n  for (const query of queries) {\n    if (\n      argsToMatch !== undefined &&\n      !Object.keys(argsToMatch).every(\n        (k) =>\n          // @ts-ignore why is this not working?\n          argsToMatch[k] === query.args[k],\n      )\n    ) {\n      continue;\n    }\n    const key = JSON.stringify(\n      Object.fromEntries(\n        Object.entries(query.args).map(([k, v]) => [\n          k,\n          k === \"paginationOpts\" ? (v as any).id : v,\n        ]),\n      ),\n    );\n    queryGroups[key] ??= [];\n    queryGroups[key].push(query);\n  }\n  for (const pageQueries of Object.values(queryGroups)) {\n    insertAtPositionInPages({\n      pageQueries,\n      paginatedQuery,\n      sortOrder,\n      sortKeyFromItem,\n      localQueryStore,\n      item,\n    });\n  }\n}\n\nfunction insertAtPositionInPages<\n  Query extends PaginatedQueryReference,\n>(options: {\n  pageQueries: LocalQueryResult<Query>[];\n  paginatedQuery: Query;\n  sortOrder: \"asc\" | \"desc\";\n  sortKeyFromItem: (element: PaginatedQueryItem<Query>) => Value | Value[];\n  localQueryStore: OptimisticLocalStore;\n  item: PaginatedQueryItem<Query>;\n}) {\n  const {\n    pageQueries,\n    sortOrder,\n    sortKeyFromItem,\n    localQueryStore,\n    item,\n    paginatedQuery,\n  } = options;\n  const insertedKey = sortKeyFromItem(item);\n  const loadedPages: LoadedResult<Query>[] = pageQueries.filter(\n    (q): q is LoadedResult<Query> =>\n      q.value !== undefined && q.value.page.length > 0,\n  );\n  const sortedPages = loadedPages.sort((a, b) => {\n    const aKey = sortKeyFromItem(a.value.page[0]);\n    const bKey = sortKeyFromItem(b.value.page[0]);\n    if (sortOrder === \"asc\") {\n      return compareValues(aKey, bKey);\n    } else {\n      return compareValues(bKey, aKey);\n    }\n  });\n\n  // check if the inserted element is before the first page\n  const firstLoadedPage = sortedPages[0];\n  if (firstLoadedPage === undefined) {\n    // no pages, so don't update until they load\n    return;\n  }\n  const firstPageKey = sortKeyFromItem(firstLoadedPage.value.page[0]);\n  const isBeforeFirstPage =\n    sortOrder === \"asc\"\n      ? compareValues(insertedKey, firstPageKey) <= 0\n      : compareValues(insertedKey, firstPageKey) >= 0;\n  if (isBeforeFirstPage) {\n    if (firstLoadedPage.args.paginationOpts.cursor === null) {\n      localQueryStore.setQuery(paginatedQuery, firstLoadedPage.args, {\n        ...firstLoadedPage.value,\n        page: [item, ...firstLoadedPage.value.page],\n      });\n    } else {\n      // if the very first page is not loaded\n      return;\n    }\n    return;\n  }\n\n  const lastLoadedPage = sortedPages[sortedPages.length - 1];\n  if (lastLoadedPage === undefined) {\n    // no pages, so don't update until they load\n    return;\n  }\n  const lastPageKey = sortKeyFromItem(\n    lastLoadedPage.value.page[lastLoadedPage.value.page.length - 1],\n  );\n  const isAfterLastPage =\n    sortOrder === \"asc\"\n      ? compareValues(insertedKey, lastPageKey) >= 0\n      : compareValues(insertedKey, lastPageKey) <= 0;\n  if (isAfterLastPage) {\n    // Only update if the last page is done loading, otherwise it will pop out\n    // when the server updates the query\n    if (lastLoadedPage.value.isDone) {\n      localQueryStore.setQuery(paginatedQuery, lastLoadedPage.args, {\n        ...lastLoadedPage.value,\n        page: [...lastLoadedPage.value.page, item],\n      });\n    }\n    return;\n  }\n\n  // if sorted in ascending order, find the first page that starts with a key greater than the inserted element,\n  // and update the page before it\n  // if sorted in descending order, find the first page that starts with a key less than the inserted element,\n  // and update the page before it\n\n  const successorPageIndex = sortedPages.findIndex((p) =>\n    sortOrder === \"asc\"\n      ? compareValues(sortKeyFromItem(p.value.page[0]), insertedKey) > 0\n      : compareValues(sortKeyFromItem(p.value.page[0]), insertedKey) < 0,\n  );\n  const pageToUpdate =\n    successorPageIndex === -1\n      ? sortedPages[sortedPages.length - 1]\n      : sortedPages[successorPageIndex - 1];\n  if (pageToUpdate === undefined) {\n    // no pages, so don't update until they load\n    return;\n  }\n  // If ascending, find the first element that is greater than or equal to the inserted element\n  // If descending, find the first element that is less than or equal to the inserted element\n  const indexWithinPage = pageToUpdate.value.page.findIndex((e) =>\n    sortOrder === \"asc\"\n      ? compareValues(sortKeyFromItem(e), insertedKey) >= 0\n      : compareValues(sortKeyFromItem(e), insertedKey) <= 0,\n  );\n  const newPage =\n    indexWithinPage === -1\n      ? [...pageToUpdate.value.page, item]\n      : [\n          ...pageToUpdate.value.page.slice(0, indexWithinPage),\n          item,\n          ...pageToUpdate.value.page.slice(indexWithinPage),\n        ];\n  localQueryStore.setQuery(paginatedQuery, pageToUpdate.args, {\n    ...pageToUpdate.value,\n    page: newPage,\n  });\n}\n", "import React from \"react\";\nimport { ReactNode } from \"react\";\nimport { useConvexAuth } from \"./ConvexAuthState.js\";\n\n/**\n * Renders children if the client is authenticated.\n *\n * @public\n */\nexport function Authenticated({ children }: { children: ReactNode }) {\n  const { isLoading, isAuthenticated } = useConvexAuth();\n  if (isLoading || !isAuthenticated) {\n    return null;\n  }\n  return <>{children}</>;\n}\n\n/**\n * Renders children if the client is using authentication but is not authenticated.\n *\n * @public\n */\nexport function Unauthenticated({ children }: { children: ReactNode }) {\n  const { isLoading, isAuthenticated } = useConvexAuth();\n  if (isLoading || isAuthenticated) {\n    return null;\n  }\n  return <>{children}</>;\n}\n\n/**\n * Renders children if the client isn't using authentication or is in the process\n * of authenticating.\n *\n * @public\n */\nexport function AuthLoading({ children }: { children: ReactNode }) {\n  const { isLoading } = useConvexAuth();\n  if (!isLoading) {\n    return null;\n  }\n  return <>{children}</>;\n}\n", "import { useMemo } from \"react\";\nimport { useQuery } from \"../react/client.js\";\nimport { FunctionReference, makeFunctionReference } from \"../server/api.js\";\nimport { jsonToConvex } from \"../values/index.js\";\n\n/**\n * The preloaded query payload, which should be passed to a client component\n * and passed to {@link usePreloadedQuery}.\n *\n * @public\n */\nexport type Preloaded<Query extends FunctionReference<\"query\">> = {\n  __type: Query;\n  _name: string;\n  _argsJSON: string;\n  _valueJSON: string;\n};\n\n/**\n * Load a reactive query within a React component using a `Preloaded` payload\n * from a Server Component returned by {@link nextjs.preloadQuery}.\n *\n * This React hook contains internal state that will cause a rerender\n * whenever the query result changes.\n *\n * Throws an error if not used under {@link ConvexProvider}.\n *\n * @param preloadedQuery - The `Preloaded` query payload from a Server Component.\n * @returns the result of the query. Initially returns the result fetched\n * by the Server Component. Subsequently returns the result fetched by the client.\n *\n * @public\n */\nexport function usePreloadedQuery<Query extends FunctionReference<\"query\">>(\n  preloadedQuery: Preloaded<Query>,\n): Query[\"_returnType\"] {\n  const args = useMemo(\n    () => jsonToConvex(preloadedQuery._argsJSON),\n    [preloadedQuery._argsJSON],\n  ) as Query[\"_args\"];\n  const preloadedResult = useMemo(\n    () => jsonToConvex(preloadedQuery._valueJSON),\n    [preloadedQuery._valueJSON],\n  );\n  const result = useQuery(\n    makeFunctionReference(preloadedQuery._name) as Query,\n    args,\n  );\n  return result === undefined ? preloadedResult : result;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAAkC;AA2DlC,IAAM,aACJ,CAAC,KAAmB,aAAqB,mBACzC,CAAC,cAAsC;AACrC,QAAM,UAAU,EAAE,GAAG,UAAU,QAAQ;AACvC,QAAM,YAAY,UAAU;AAC5B,QAAM,YAAY,UAAU,cAAc;AAC1C,QAAM,cAAc,UAAU,cAAc;AAC5C,UAAQ,SAAS,IAAI;IACnB,OAAO,UAAU;IACjB,MAAM;MACJ,GAAG,UAAU;MACb,gBAAgB;QACd,GAAG,UAAU,QAAQ,GAAG,EAAE,KAAK;QAC/B,WAAW;MACb;IACF;EACF;AACA,UAAQ,SAAS,IAAI;IACnB,OAAO,UAAU;IACjB,MAAM;MACJ,GAAG,UAAU;MACb,gBAAgB;QACd,GAAG,UAAU,QAAQ,GAAG,EAAE,KAAK;QAC/B,QAAQ;QACR,WAAW;MACb;IACF;EACF;AACA,QAAM,gBAAgB,EAAE,GAAG,UAAU,cAAc;AACnD,gBAAc,GAAG,IAAI,CAAC,WAAW,SAAS;AAC1C,SAAO;IACL,GAAG;IACH;IACA;IACA;EACF;AACF;AAEF,IAAM,qBACJ,CAAC,QAAsB,CAAC,cAAsC;AAC5D,QAAM,iBAAiB,UAAU,cAAc,GAAG;AAClD,MAAI,mBAAmB,QAAW;AAChC,WAAO;EACT;AACA,QAAM,UAAU,EAAE,GAAG,UAAU,QAAQ;AACvC,SAAO,QAAQ,GAAG;AAClB,QAAM,gBAAgB,EAAE,GAAG,UAAU,cAAc;AACnD,SAAO,cAAc,GAAG;AACxB,MAAI,WAAW,UAAU,SAAS,MAAM;AACxC,QAAM,YAAY,UAAU,SAAS,UAAU,CAAC,MAAM,MAAM,GAAG;AAC/D,MAAI,aAAa,GAAG;AAClB,eAAW;MACT,GAAG,UAAU,SAAS,MAAM,GAAG,SAAS;MACxC,GAAG;MACH,GAAG,UAAU,SAAS,MAAM,YAAY,CAAC;IAC3C;EACF;AACA,SAAO;IACL,GAAG;IACH;IACA;IACA;EACF;AACF;AAuCK,SAAS,kBACd,OACA,MACA,SACoC;AACpC,MACE,QAAO,mCAAS,qBAAoB,YACpC,QAAQ,kBAAkB,GAC1B;AACA,UAAM,IAAI;MACR,qEAAqE,mCAAS,eAAe;IAC/F;EACF;AACA,QAAM,OAAO,SAAS;AACtB,QAAM,aAAa,OAAO,CAAC,IAAI;AAC/B,QAAM,YAAY,gBAAgB,KAAK;AACvC,QAAM,yBAAqB,sBAAQ,MAAM;AACvC,WAAO,MAAM;AACX,YAAM,KAAK,iBAAiB;AAC5B,aAAO;QACL;QACA,MAAM;QACN;QACA,aAAa;QACb,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC;QACxB,SAAS,OACJ,CAAC,IACF;UACE,GAAG;YACD;YACA,MAAM;cACJ,GAAG;cACH,gBAAgB;gBACd,UAAU,QAAQ;gBAClB,QAAQ;gBACR;cACF;YACF;UACF;QACF;QACJ,eAAe,CAAC;QAChB;MACF;IACF;EAKF,GAAG;;IAED,KAAK,UAAU,aAAa,UAAmB,CAAC;IAChD;IACA,QAAQ;IACR;EACF,CAAC;AAED,QAAM,CAAC,OAAO,QAAQ,QACpB,uBAAiC,kBAAkB;AAGrD,MAAI,YAAY;AAChB,MACE,gBAAgB,KAAK,MAAM,gBAAgB,MAAM,KAAK,KACtD,KAAK,UAAU,aAAa,UAAmB,CAAC,MAC9C,KAAK,UAAU,aAAa,MAAM,IAAI,CAAC,KACzC,SAAS,MAAM,MACf;AACA,gBAAY,mBAAmB;AAC/B,aAAS,SAAS;EACpB;AACA,QAAM,eAAe,UAAU;AAC/B,QAAM,SAAS,aAAa;AAE5B,QAAM,gBAAgB,WAAW,UAAU,OAAO;AAElD,QAAM,CAAC,SAAS,eAAe,QAG3B,sBAAQ,MAAM;;AAChB,QAAI,aAAa;AAEjB,UAAM,WAAW,CAAC;AAClB,eAAW,WAAW,UAAU,UAAU;AACxC,mBAAa,cAAc,OAAO;AAClC,UAAI,eAAe,QAAW;AAC5B;MACF;AAEA,UAAI,sBAAsB,OAAO;AAC/B,YACE,WAAW,QAAQ,SAAS,eAAe,KAC1C,sBAAsB,eACrB,OAAO,WAAW,SAAS,cAC3B,gBAAW,SAAX,mBAAiB,yBAAwB,UACzC,gBAAW,SAAX,mBAAiB,qBAAoB,iBACvC;AAQA,iBAAO;YACL,8DACE,WAAW;UACf;AACA,mBAAS,kBAAkB;AAC3B,iBAAO,CAAC,CAAC,GAAG,MAAS;QACvB,OAAO;AACL,gBAAM;QACR;MACF;AACA,YAAM,eAAe,UAAU,cAAc,OAAO;AACpD,UAAI,iBAAiB,QAAW;AAC9B,YACE,cAAc,aAAa,CAAC,CAAC,MAAM,UACnC,cAAc,aAAa,CAAC,CAAC,MAAM,QACnC;AAEA,mBAAS,mBAAmB,OAAO,CAAC;QACtC;MACF,WACE,WAAW,gBACV,WAAW,eAAe,sBACzB,WAAW,eAAe,mBAC1B,WAAW,KAAK,SAAS,QAAQ,kBAAkB,IACrD;AAGA;UACE;YACE;YACA,WAAW;YACX,WAAW;UACb;QACF;MACF;AACA,UAAI,WAAW,eAAe,iBAAiB;AAI7C,eAAO,CAAC,UAAU,MAAS;MAC7B;AACA,eAAS,KAAK,GAAG,WAAW,IAAI;IAClC;AACA,WAAO,CAAC,UAAU,UAAU;EAC9B,GAAG;IACD;IACA,UAAU;IACV,UAAU;IACV,QAAQ;IACR;IACA;EACF,CAAC;AAED,QAAM,mBAAe,sBAAQ,MAAM;AACjC,QAAI,oBAAoB,QAAW;AACjC,UAAI,UAAU,gBAAgB,GAAG;AAC/B,eAAO;UACL,QAAQ;UACR,WAAW;UACX,UAAU,CAAC,cAAsB;UAEjC;QACF;MACF,OAAO;AACL,eAAO;UACL,QAAQ;UACR,WAAW;UACX,UAAU,CAAC,cAAsB;UAEjC;QACF;MACF;IACF;AACA,QAAI,gBAAgB,QAAQ;AAC1B,aAAO;QACL,QAAQ;QACR,WAAW;QACX,UAAU,CAAC,cAAsB;QAEjC;MACF;IACF;AACA,UAAM,iBAAiB,gBAAgB;AACvC,QAAI,qBAAqB;AACzB,WAAO;MACL,QAAQ;MACR,WAAW;MACX,UAAU,CAAC,aAAqB;AAC9B,YAAI,CAAC,oBAAoB;AACvB,+BAAqB;AACrB,mBAAS,CAAC,cAAc;AACtB,kBAAM,WAAW,CAAC,GAAG,UAAU,UAAU,UAAU,WAAW;AAC9D,kBAAM,UAAU,EAAE,GAAG,UAAU,QAAQ;AACvC,oBAAQ,UAAU,WAAW,IAAI;cAC/B,OAAO,UAAU;cACjB,MAAM;gBACJ,GAAG,UAAU;gBACb,gBAAgB;kBACd;kBACA,QAAQ;kBACR,IAAI,UAAU;gBAChB;cACF;YACF;AACA,mBAAO;cACL,GAAG;cACH,aAAa,UAAU,cAAc;cACrC;cACA;YACF;UACF,CAAC;QACH;MACF;IACF;EACF,GAAG,CAAC,iBAAiB,UAAU,WAAW,CAAC;AAE3C,SAAO;IACL;IACA,GAAG;EACL;AACF;AAEA,IAAI,eAAe;AAyBnB,SAAS,mBAA2B;AAClC;AACA,SAAO;AACT;AAKO,SAAS,oBAAoB;AAClC,iBAAe;AACjB;AAsHO,SAAS,0CAGd,YACA,OACA,MACA,aAGM;AACN,QAAM,eAAe,KAAK,UAAU,aAAa,IAAa,CAAC;AAE/D,aAAW,eAAe,WAAW,cAAc,KAAK,GAAG;AACzD,QAAI,YAAY,UAAU,QAAW;AACnC,YAAM,EAAE,gBAAgB,GAAG,GAAG,UAAU,IAAI,YAAY;AAGxD,UAAI,KAAK,UAAU,aAAa,SAAkB,CAAC,MAAM,cAAc;AACrE,cAAM,QAAQ,YAAY;AAC1B,YACE,OAAO,UAAU,YACjB,UAAU,QACV,MAAM,QAAQ,MAAM,IAAI,GACxB;AACA,qBAAW,SAAS,OAAO,YAAY,MAAM;YAC3C,GAAG;YACH,MAAM,MAAM,KAAK,IAAI,WAAW;UAClC,CAAC;QACH;MACF;IACF;EACF;AACF;AA8BO,SAAS,YAAmD,SAKhE;AACD,QAAM,EAAE,gBAAgB,aAAa,iBAAiB,KAAK,IAAI;AAC/D,QAAM,UAAU,gBAAgB,cAAc,cAAc;AAC5D,QAAM,mBAAmB,QAAQ,OAAO,CAAC,MAAM;AAC7C,QAAI,gBAAgB,QAAW;AAC7B,aAAO;IACT;AACA,WAAO,OAAO,KAAK,WAAW,EAAE;;MAE9B,CAAC,MAAM,cAAc,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,MAAM;IACtD;EACF,CAAC;AACD,QAAM,YAAY,iBAAiB;IACjC,CAAC,MAAM,EAAE,KAAK,eAAe,WAAW;EAC1C;AACA,MAAI,cAAc,UAAa,UAAU,UAAU,QAAW;AAE5D;EACF;AACA,kBAAgB,SAAS,gBAAgB,UAAU,MAAM;IACvD,GAAG,UAAU;IACb,MAAM,CAAC,MAAM,GAAG,UAAU,MAAM,IAAI;EACtC,CAAC;AACH;AAqBO,SAAS,uBAEd,SAKC;AACD,QAAM,EAAE,gBAAgB,iBAAiB,MAAM,YAAY,IAAI;AAC/D,QAAM,UAAU,gBAAgB,cAAc,cAAc;AAC5D,QAAM,mBAAmB,QAAQ,OAAO,CAAC,MAAM;AAC7C,QAAI,gBAAgB,QAAW;AAC7B,aAAO;IACT;AACA,WAAO,OAAO,KAAK,WAAW,EAAE;;MAE9B,CAAC,MAAM,cAAc,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,MAAM;IACtD;EACF,CAAC;AACD,QAAM,WAAW,iBAAiB;IAChC,CAAC,MAAM,EAAE,UAAU,UAAa,EAAE,MAAM;EAC1C;AACA,MAAI,aAAa,QAAW;AAG1B;EACF;AACA,kBAAgB,SAAS,gBAAgB,SAAS,MAAM;IACtD,GAAG,SAAS;IACZ,MAAM,CAAC,GAAG,SAAS,MAAO,MAAM,IAAI;EACtC,CAAC;AACH;AAkDO,SAAS,iBAEd,SAOC;AACD,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AAEJ,QAAM,UACJ,gBAAgB,cAAc,cAAc;AAG9C,QAAM,cAAyD,CAAC;AAChE,aAAW,SAAS,SAAS;AAC3B,QACE,gBAAgB,UAChB,CAAC,OAAO,KAAK,WAAW,EAAE;MACxB,CAAC;;QAEC,YAAY,CAAC,MAAM,MAAM,KAAK,CAAC;;IACnC,GACA;AACA;IACF;AACA,UAAM,MAAM,KAAK;MACf,OAAO;QACL,OAAO,QAAQ,MAAM,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM;UACzC;UACA,MAAM,mBAAoB,EAAU,KAAK;QAC3C,CAAC;MACH;IACF;AACA,gBAAA,GAAA,MAAA,YAAA,GAAA,IAAqB,CAAC;AACtB,gBAAY,GAAG,EAAE,KAAK,KAAK;EAC7B;AACA,aAAW,eAAe,OAAO,OAAO,WAAW,GAAG;AACpD,4BAAwB;MACtB;MACA;MACA;MACA;MACA;MACA;IACF,CAAC;EACH;AACF;AAEA,SAAS,wBAEP,SAOC;AACD,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AACJ,QAAM,cAAc,gBAAgB,IAAI;AACxC,QAAM,cAAqC,YAAY;IACrD,CAAC,MACC,EAAE,UAAU,UAAa,EAAE,MAAM,KAAK,SAAS;EACnD;AACA,QAAM,cAAc,YAAY,KAAK,CAAC,GAAG,MAAM;AAC7C,UAAM,OAAO,gBAAgB,EAAE,MAAM,KAAK,CAAC,CAAC;AAC5C,UAAM,OAAO,gBAAgB,EAAE,MAAM,KAAK,CAAC,CAAC;AAC5C,QAAI,cAAc,OAAO;AACvB,aAAO,cAAc,MAAM,IAAI;IACjC,OAAO;AACL,aAAO,cAAc,MAAM,IAAI;IACjC;EACF,CAAC;AAGD,QAAM,kBAAkB,YAAY,CAAC;AACrC,MAAI,oBAAoB,QAAW;AAEjC;EACF;AACA,QAAM,eAAe,gBAAgB,gBAAgB,MAAM,KAAK,CAAC,CAAC;AAClE,QAAM,oBACJ,cAAc,QACV,cAAc,aAAa,YAAY,KAAK,IAC5C,cAAc,aAAa,YAAY,KAAK;AAClD,MAAI,mBAAmB;AACrB,QAAI,gBAAgB,KAAK,eAAe,WAAW,MAAM;AACvD,sBAAgB,SAAS,gBAAgB,gBAAgB,MAAM;QAC7D,GAAG,gBAAgB;QACnB,MAAM,CAAC,MAAM,GAAG,gBAAgB,MAAM,IAAI;MAC5C,CAAC;IACH,OAAO;AAEL;IACF;AACA;EACF;AAEA,QAAM,iBAAiB,YAAY,YAAY,SAAS,CAAC;AACzD,MAAI,mBAAmB,QAAW;AAEhC;EACF;AACA,QAAM,cAAc;IAClB,eAAe,MAAM,KAAK,eAAe,MAAM,KAAK,SAAS,CAAC;EAChE;AACA,QAAM,kBACJ,cAAc,QACV,cAAc,aAAa,WAAW,KAAK,IAC3C,cAAc,aAAa,WAAW,KAAK;AACjD,MAAI,iBAAiB;AAGnB,QAAI,eAAe,MAAM,QAAQ;AAC/B,sBAAgB,SAAS,gBAAgB,eAAe,MAAM;QAC5D,GAAG,eAAe;QAClB,MAAM,CAAC,GAAG,eAAe,MAAM,MAAM,IAAI;MAC3C,CAAC;IACH;AACA;EACF;AAOA,QAAM,qBAAqB,YAAY;IAAU,CAAC,MAChD,cAAc,QACV,cAAc,gBAAgB,EAAE,MAAM,KAAK,CAAC,CAAC,GAAG,WAAW,IAAI,IAC/D,cAAc,gBAAgB,EAAE,MAAM,KAAK,CAAC,CAAC,GAAG,WAAW,IAAI;EACrE;AACA,QAAM,eACJ,uBAAuB,KACnB,YAAY,YAAY,SAAS,CAAC,IAClC,YAAY,qBAAqB,CAAC;AACxC,MAAI,iBAAiB,QAAW;AAE9B;EACF;AAGA,QAAM,kBAAkB,aAAa,MAAM,KAAK;IAAU,CAAC,MACzD,cAAc,QACV,cAAc,gBAAgB,CAAC,GAAG,WAAW,KAAK,IAClD,cAAc,gBAAgB,CAAC,GAAG,WAAW,KAAK;EACxD;AACA,QAAM,UACJ,oBAAoB,KAChB,CAAC,GAAG,aAAa,MAAM,MAAM,IAAI,IACjC;IACE,GAAG,aAAa,MAAM,KAAK,MAAM,GAAG,eAAe;IACnD;IACA,GAAG,aAAa,MAAM,KAAK,MAAM,eAAe;EAClD;AACN,kBAAgB,SAAS,gBAAgB,aAAa,MAAM;IAC1D,GAAG,aAAa;IAChB,MAAM;EACR,CAAC;AACH;;;AC34BA,IAAAA,gBAAkB;AASX,SAAS,cAAc,EAAE,SAAS,GAA4B;AACnE,QAAM,EAAE,WAAW,gBAAgB,IAAI,cAAc;AACrD,MAAI,aAAa,CAAC,iBAAiB;AACjC,WAAO;EACT;AACA,SAAO,cAAAC,QAAA,cAAA,cAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAOO,SAAS,gBAAgB,EAAE,SAAS,GAA4B;AACrE,QAAM,EAAE,WAAW,gBAAgB,IAAI,cAAc;AACrD,MAAI,aAAa,iBAAiB;AAChC,WAAO;EACT;AACA,SAAO,cAAAA,QAAA,cAAA,cAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;AAQO,SAAS,YAAY,EAAE,SAAS,GAA4B;AACjE,QAAM,EAAE,UAAU,IAAI,cAAc;AACpC,MAAI,CAAC,WAAW;AACd,WAAO;EACT;AACA,SAAO,cAAAA,QAAA,cAAA,cAAAA,QAAA,UAAA,MAAG,QAAS;AACrB;;;AC1CA,IAAAC,gBAAwB;AAiCjB,SAAS,kBACd,gBACsB;AACtB,QAAM,WAAO;IACX,MAAM,aAAa,eAAe,SAAS;IAC3C,CAAC,eAAe,SAAS;EAC3B;AACA,QAAM,sBAAkB;IACtB,MAAM,aAAa,eAAe,UAAU;IAC5C,CAAC,eAAe,UAAU;EAC5B;AACA,QAAM,SAAS;IACb,sBAAsB,eAAe,KAAK;IAC1C;EACF;AACA,SAAO,WAAW,SAAY,kBAAkB;AAClD;", "names": ["import_react", "React", "import_react"]}