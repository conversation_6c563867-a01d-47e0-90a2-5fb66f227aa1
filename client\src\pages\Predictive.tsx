import Navigation from "@/components/Navigation";
import MetricsChart from "@/components/MetricsChart";
import StatusCard from "@/components/StatusCard";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Brain, Activity, LineChart, <PERSON><PERSON><PERSON>riangle, Clock, Gauge } from "lucide-react";

const Predictive = () => {
  const now = new Date();
  const timeLabels = Array.from({ length: 12 }, (_, i) => {
    const t = new Date(now.getTime() - (11 - i) * 5 * 60 * 1000);
    return t.toLocaleTimeString("en-US", { hour: "2-digit", minute: "2-digit" });
  });

  // Demo historical and forecasted latency (ms)
  const latencyForecast = timeLabels.map((time, idx) => {
    const base = 140 + Math.sin(idx / 2) * 15;
    const noise = (Math.random() - 0.5) * 12;
    const value = Math.max(80, Math.round(base + noise));
    return { time, value };
  });

  // Demo anomaly score (0-1)
  const anomalyScore = timeLabels.map((time, idx) => {
    const base = 0.15 + Math.abs(Math.sin(idx / 3)) * 0.15;
    const noise = (Math.random() - 0.5) * 0.05;
    const value = Math.max(0, Number((base + noise).toFixed(2)));
    return { time, value };
  });

  // Demo risk index (0-100)
  const riskIndex = timeLabels.map((time, idx) => {
    const base = 35 + Math.cos(idx / 2.5) * 10;
    const noise = (Math.random() - 0.5) * 8;
    const value = Math.max(0, Math.min(100, Math.round(base + noise)));
    return { time, value };
  });

  // Demo capacity forecast (requests/min)
  const capacityForecast = timeLabels.map((time, idx) => {
    const trend = 900 + idx * 15;
    const noise = (Math.random() - 0.5) * 60;
    const value = Math.max(300, Math.round(trend + noise));
    return { time, value };
  });

  // Headline demo KPIs
  const kpis = {
    predictedUptime: 99.3,
    forecastLatency: 152,
    failureRisk: 28,
    capacityHeadroom: 62,
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-start justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">Predictive Monitoring</h1>
            <p className="text-muted-foreground">Proactive insights with anomaly detection, risk forecasting, and capacity planning</p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className="bg-primary/20 text-primary border-primary/30 flex items-center gap-1">
              <Brain className="w-3 h-3" />
              AI Models: v0.3 demo
            </Badge>
            <Button variant="cyber" size="sm">Run What-If</Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatusCard
            title="Predicted Uptime"
            value={`${kpis.predictedUptime}%`}
            subtitle="Next 24h"
            status={kpis.predictedUptime >= 99 ? "success" : kpis.predictedUptime >= 97 ? "warning" : "error"}
            trend="up"
            trendValue="+0.2%"
            icon={<Activity className="w-4 h-4" />}
          />

          <StatusCard
            title="Forecast Latency"
            value={`${kpis.forecastLatency}ms`}
            subtitle="P50 next 1h"
            status={kpis.forecastLatency <= 200 ? "success" : kpis.forecastLatency <= 400 ? "warning" : "error"}
            trend={kpis.forecastLatency <= 200 ? "down" : "up"}
            trendValue={kpis.forecastLatency <= 200 ? "-6ms" : "+12ms"}
            icon={<Clock className="w-4 h-4" />}
          />

          <StatusCard
            title="Failure Risk"
            value={`${kpis.failureRisk}%`}
            subtitle="Incident probability"
            status={kpis.failureRisk < 20 ? "success" : kpis.failureRisk < 50 ? "warning" : "error"}
            trend={kpis.failureRisk < 20 ? "down" : "up"}
            trendValue={kpis.failureRisk < 20 ? "-3%" : "+4%"}
            icon={<AlertTriangle className="w-4 h-4" />}
          />

          <StatusCard
            title="Capacity Headroom"
            value={`${kpis.capacityHeadroom}%`}
            subtitle="Before saturation"
            status={kpis.capacityHeadroom > 50 ? "success" : kpis.capacityHeadroom > 25 ? "warning" : "error"}
            trend={kpis.capacityHeadroom > 50 ? "up" : "down"}
            trendValue={kpis.capacityHeadroom > 50 ? "+5%" : "-7%"}
            icon={<Gauge className="w-4 h-4" />}
          />
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <MetricsChart
            title="Latency Forecast"
            subtitle="Projected response time (P50)"
            data={latencyForecast}
            dataKey="value"
            type="area"
            color="hsl(200, 98%, 60%)"
            height={260}
          />

          <MetricsChart
            title="Anomaly Score"
            subtitle="Probabilistic anomaly likelihood"
            data={anomalyScore}
            dataKey="value"
            type="line"
            color="hsl(280, 100%, 70%)"
            height={260}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <MetricsChart
            title="Risk Index Forecast"
            subtitle="Composite risk score (0-100)"
            data={riskIndex}
            dataKey="value"
            type="area"
            color="hsl(0, 84%, 60%)"
            height={240}
          />

          <MetricsChart
            title="Capacity Forecast"
            subtitle="Requests per minute before bottlenecks"
            data={capacityForecast}
            dataKey="value"
            type="line"
            color="hsl(142, 76%, 36%)"
            height={240}
          />
        </div>

        {/* Model Notes */}
        <div className="mt-8">
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <LineChart className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-semibold text-foreground">About this demo</h2>
            </div>
            <p className="text-sm text-muted-foreground">
              This page uses synthetic data to demonstrate predictive monitoring concepts: anomaly scores, risk forecasting, and capacity planning. Integrate real models by feeding provider telemetry into your ML pipeline and wiring predictions here.
            </p>
          </Card>
        </div>
      </main>
    </div>
  );
};

export default Predictive;


